"""
Main FastAPI application for the Database Analyst System.
Provides REST API endpoints for agent communication and workflow orchestration.
"""

import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import <PERSON><PERSON><PERSON>, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import uvicorn

from api.routers import workflows, agents, health, system
from api.middleware.logging import LoggingMiddleware
from api.middleware.rate_limiting import RateLimitingMiddleware, rate_limit_handler
from api.validators import handle_validation_exception
from slowapi.errors import RateLimitExceeded
from config.settings import get_settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown events."""
    # Startup
    logger.info("Starting Database Analyst API...")
    
    # Initialize database connection
    try:
        from database.database_config import get_database_config
        db_config = get_database_config()
        db_config.initialize_engine()
        logger.info("Database connection initialized")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise
    
    # Initialize agent coordinator
    try:
        from agents.agent_coordinator import get_agent_coordinator
        coordinator = get_agent_coordinator()
        logger.info("Agent coordinator initialized")
    except Exception as e:
        logger.error(f"Failed to initialize agent coordinator: {e}")
        raise
    
    logger.info("Database Analyst API started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Database Analyst API...")
    
    # Close database connections
    try:
        db_config.close_engine()
        logger.info("Database connections closed")
    except Exception as e:
        logger.error(f"Error closing database connections: {e}")
    
    logger.info("Database Analyst API shutdown complete")


# Create FastAPI application
app = FastAPI(
    title="Database Analyst API",
    description="""
    ## 🤖 Intelligent Database Analysis API

    A comprehensive REST API for database analysis using AI-powered agents.

    ### Features

    * **🔍 Natural Language Queries**: Convert natural language to SQL
    * **📊 Advanced Analytics**: Comprehensive data analysis with pandas
    * **🏗️ Schema Analysis**: Database structure analysis and optimization
    * **⚡ Workflow Orchestration**: Multi-agent workflow management
    * **📈 Real-time Monitoring**: System health and performance metrics
    * **🔒 Security**: Rate limiting, input validation, and error handling

    ### Quick Start

    1. **Health Check**: `GET /health` - Check system status
    2. **Create Workflow**: `POST /workflows` - Start a new analysis
    3. **Execute Query**: `POST /agents/sql/query` - Run SQL queries
    4. **Analyze Data**: `POST /agents/pandas/analyze` - Perform data analysis

    ### Authentication

    Currently, the API operates without authentication. In production environments,
    implement proper authentication and authorization mechanisms.

    ### Rate Limits

    - General endpoints: 60 requests per minute
    - SQL queries: 30 requests per minute
    - Data analysis: 15 requests per minute
    - Batch operations: 5 requests per minute
    """,
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan,
    contact={
        "name": "Database Analyst System",
        "url": "https://github.com/your-repo/database-analyst",
        "email": "<EMAIL>"
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT"
    },
    servers=[
        {
            "url": "http://localhost:8000",
            "description": "Development server"
        },
        {
            "url": "https://api.example.com",
            "description": "Production server"
        }
    ],
    tags_metadata=[
        {
            "name": "Health",
            "description": "System health checks and monitoring endpoints"
        },
        {
            "name": "System",
            "description": "System information and configuration endpoints"
        },
        {
            "name": "Workflows",
            "description": "Workflow management and orchestration endpoints"
        },
        {
            "name": "Agents",
            "description": "Direct agent communication endpoints"
        }
    ]
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"]  # Configure appropriately for production
)

app.add_middleware(LoggingMiddleware)
app.add_middleware(RateLimitingMiddleware)


# Exception handlers
@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """Handle HTTP exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": "HTTP Exception",
            "detail": exc.detail,
            "status_code": exc.status_code
        }
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle request validation errors."""
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": "Validation Error",
            "detail": exc.errors(),
            "body": exc.body
        }
    )


@app.exception_handler(RateLimitExceeded)
async def rate_limit_exception_handler(request: Request, exc: RateLimitExceeded):
    """Handle rate limit exceeded exceptions."""
    return await rate_limit_handler(request, exc)


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": "Internal Server Error",
            "detail": "An unexpected error occurred"
        }
    )


# Include routers
app.include_router(health.router, prefix="/health", tags=["Health"])
app.include_router(system.router, prefix="/system", tags=["System"])
app.include_router(workflows.router, prefix="/workflows", tags=["Workflows"])
app.include_router(agents.router, prefix="/agents", tags=["Agents"])

# Import and include upload router
from api.routers import upload
app.include_router(upload.router, prefix="/upload", tags=["Data Upload"])

# Include testing router (optional - can be disabled in production)
from api.testing import test_router
app.include_router(test_router, tags=["Testing"])


@app.get("/", response_model=Dict[str, Any])
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Database Analyst API",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs",
        "redoc": "/redoc"
    }


if __name__ == "__main__":
    uvicorn.run(
        "api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.app.debug,
        log_level=settings.app.log_level.lower()
    )
