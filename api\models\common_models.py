"""
Common Pydantic models for API responses and shared data structures.
"""

from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum


class ResponseStatus(str, Enum):
    """Enumeration for response status values."""
    SUCCESS = "success"
    ERROR = "error"
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class BaseResponse(BaseModel):
    """Base response model for all API endpoints."""
    status: ResponseStatus = Field(..., description="Response status")
    message: str = Field(..., description="Human-readable message")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")
    request_id: Optional[str] = Field(None, description="Request tracking ID")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ErrorResponse(BaseResponse):
    """Error response model with additional error details."""
    status: ResponseStatus = Field(ResponseStatus.ERROR, description="Error status")
    error_code: Optional[str] = Field(None, description="Machine-readable error code")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Additional error information")
    stack_trace: Optional[str] = Field(None, description="Stack trace for debugging")


class SuccessResponse(BaseResponse):
    """Success response model with data payload."""
    status: ResponseStatus = Field(ResponseStatus.SUCCESS, description="Success status")
    data: Optional[Dict[str, Any]] = Field(None, description="Response data payload")


class HealthStatus(str, Enum):
    """Health check status enumeration."""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    DEGRADED = "degraded"


class ComponentHealth(BaseModel):
    """Health status for individual system components."""
    name: str = Field(..., description="Component name")
    status: HealthStatus = Field(..., description="Component health status")
    message: Optional[str] = Field(None, description="Health status message")
    last_check: datetime = Field(default_factory=datetime.now, description="Last health check time")
    response_time: Optional[float] = Field(None, description="Component response time in seconds")


class HealthResponse(BaseResponse):
    """Health check response model."""
    status: ResponseStatus = Field(ResponseStatus.SUCCESS, description="Overall health status")
    overall_health: HealthStatus = Field(..., description="Overall system health")
    components: List[ComponentHealth] = Field(default_factory=list, description="Individual component health")
    uptime: Optional[float] = Field(None, description="System uptime in seconds")
    version: Optional[str] = Field(None, description="API version")


class SystemMetrics(BaseModel):
    """System performance metrics."""
    active_workflows: int = Field(0, description="Number of active workflows")
    total_requests: int = Field(0, description="Total requests processed")
    average_response_time: float = Field(0.0, description="Average response time in seconds")
    error_rate: float = Field(0.0, description="Error rate percentage")
    memory_usage: Optional[float] = Field(None, description="Memory usage percentage")
    cpu_usage: Optional[float] = Field(None, description="CPU usage percentage")


class SystemStatusResponse(BaseResponse):
    """System status response with detailed metrics."""
    status: ResponseStatus = Field(ResponseStatus.SUCCESS, description="System status")
    metrics: SystemMetrics = Field(..., description="System performance metrics")
    database_status: HealthStatus = Field(..., description="Database connection status")
    agent_status: Dict[str, HealthStatus] = Field(default_factory=dict, description="Agent availability status")


class PaginationParams(BaseModel):
    """Pagination parameters for list endpoints."""
    page: int = Field(1, ge=1, description="Page number (1-based)")
    page_size: int = Field(20, ge=1, le=100, description="Number of items per page")
    
    @property
    def offset(self) -> int:
        """Calculate offset for database queries."""
        return (self.page - 1) * self.page_size


class PaginatedResponse(BaseResponse):
    """Paginated response model."""
    status: ResponseStatus = Field(ResponseStatus.SUCCESS, description="Response status")
    data: List[Any] = Field(default_factory=list, description="Response data items")
    pagination: Dict[str, Any] = Field(..., description="Pagination metadata")
    
    @classmethod
    def create(
        cls,
        data: List[Any],
        page: int,
        page_size: int,
        total_count: int,
        message: str = "Data retrieved successfully"
    ) -> "PaginatedResponse":
        """Create paginated response with metadata."""
        total_pages = (total_count + page_size - 1) // page_size
        
        return cls(
            message=message,
            data=data,
            pagination={
                "page": page,
                "page_size": page_size,
                "total_count": total_count,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_previous": page > 1
            }
        )


class ValidationError(BaseModel):
    """Validation error details."""
    field: str = Field(..., description="Field name with validation error")
    message: str = Field(..., description="Validation error message")
    value: Optional[Any] = Field(None, description="Invalid value")


class ValidationErrorResponse(ErrorResponse):
    """Response for validation errors."""
    error_code: str = Field("VALIDATION_ERROR", description="Validation error code")
    validation_errors: List[ValidationError] = Field(default_factory=list, description="Field validation errors")


# Common field validators and examples
class ExampleModels:
    """Example model instances for API documentation."""
    
    SUCCESS_RESPONSE = SuccessResponse(
        message="Operation completed successfully",
        data={"result": "example_data"}
    )
    
    ERROR_RESPONSE = ErrorResponse(
        message="An error occurred",
        error_code="INTERNAL_ERROR",
        error_details={"detail": "Example error details"}
    )
    
    HEALTH_RESPONSE = HealthResponse(
        message="System health check completed",
        overall_health=HealthStatus.HEALTHY,
        components=[
            ComponentHealth(
                name="database",
                status=HealthStatus.HEALTHY,
                message="Database connection active",
                response_time=0.05
            )
        ],
        uptime=3600.0,
        version="1.0.0"
    )
