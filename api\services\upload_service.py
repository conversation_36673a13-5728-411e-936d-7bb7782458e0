"""
Upload service for handling file uploads and data ingestion.
Manages the complete upload workflow from file validation to database insertion.
"""

import logging
import uuid
import tempfile
import os
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
import pandas as pd
from fastapi import UploadFile

from api.models.upload_models import (
    UploadRequest, UploadResponse, UploadStatusResponse, UploadListResponse,
    DataPreviewResponse, UploadStatus, DataType, FileFormat, ProcessingResult,
    DataValidationResult
)
from database.database_config import get_database_config
from utils.data_pipeline import PipelineDatos
from config.settings import get_settings

logger = logging.getLogger(__name__)

class UploadService:
    """Service for handling file uploads and data processing."""
    
    def __init__(self):
        """Initialize upload service."""
        self.db_config = get_database_config()
        self.data_pipeline = PipelineDatos()
        self.settings = get_settings()
        
        # In-memory storage for upload tracking (in production, use Redis or database)
        self.uploads: Dict[str, Dict[str, Any]] = {}
        self.upload_data: Dict[str, pd.DataFrame] = {}
    
    async def process_upload(self, file: UploadFile, request: UploadRequest) -> UploadResponse:
        """Process uploaded file and create upload record."""
        try:
            # Generate upload ID
            upload_id = f"upload_{uuid.uuid4().hex}"
            
            # Read file content
            content = await file.read()
            
            # Detect file format
            file_format = self._detect_file_format(file.filename, file.content_type)
            
            # Parse file content
            df = await self._parse_file_content(content, file_format, file.filename)
            
            # Generate table name if not provided
            table_name = request.table_name or self._generate_table_name(file.filename, request.data_type)
            
            # Create upload record
            upload_record = {
                "upload_id": upload_id,
                "status": UploadStatus.PENDING,
                "message": "File uploaded successfully, queued for processing",
                "file_info": {
                    "filename": file.filename,
                    "size": len(content),
                    "format": file_format.value,
                    "rows": len(df),
                    "columns": len(df.columns)
                },
                "table_name": table_name,
                "request": request,
                "created_at": datetime.now(),
                "started_at": None,
                "completed_at": None,
                "progress": 0.0,
                "error_details": None
            }
            
            # Store upload record and data
            self.uploads[upload_id] = upload_record
            self.upload_data[upload_id] = df
            
            # Start background processing
            asyncio.create_task(self._process_upload_background(upload_id))
            
            # Return response
            return UploadResponse(
                upload_id=upload_id,
                status=UploadStatus.PENDING,
                message=upload_record["message"],
                file_info=upload_record["file_info"],
                table_name=table_name,
                created_at=upload_record["created_at"]
            )
            
        except Exception as e:
            logger.error(f"Failed to process upload: {e}")
            raise
    
    async def get_upload_status(self, upload_id: str) -> UploadStatusResponse:
        """Get upload status."""
        if upload_id not in self.uploads:
            raise ValueError(f"Upload {upload_id} not found")
        
        record = self.uploads[upload_id]
        
        return UploadStatusResponse(
            upload_id=upload_id,
            status=record["status"],
            progress=record["progress"],
            message=record["message"],
            created_at=record["created_at"],
            started_at=record["started_at"],
            completed_at=record["completed_at"],
            error_details=record["error_details"]
        )
    
    async def list_uploads(
        self, 
        status_filter: Optional[UploadStatus] = None,
        page: int = 1,
        page_size: int = 20
    ) -> UploadListResponse:
        """List uploads with filtering and pagination."""
        # Filter uploads
        filtered_uploads = []
        for upload_id, record in self.uploads.items():
            if status_filter is None or record["status"] == status_filter:
                filtered_uploads.append(UploadStatusResponse(
                    upload_id=upload_id,
                    status=record["status"],
                    progress=record["progress"],
                    message=record["message"],
                    created_at=record["created_at"],
                    started_at=record["started_at"],
                    completed_at=record["completed_at"],
                    error_details=record["error_details"]
                ))
        
        # Sort by creation time (newest first)
        filtered_uploads.sort(key=lambda x: x.created_at, reverse=True)
        
        # Apply pagination
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_uploads = filtered_uploads[start_idx:end_idx]
        
        return UploadListResponse(
            uploads=paginated_uploads,
            total=len(filtered_uploads),
            page=page,
            page_size=page_size
        )
    
    async def cancel_upload(self, upload_id: str) -> bool:
        """Cancel an upload."""
        if upload_id not in self.uploads:
            raise ValueError(f"Upload {upload_id} not found")
        
        record = self.uploads[upload_id]
        
        if record["status"] in [UploadStatus.PENDING, UploadStatus.PROCESSING]:
            record["status"] = UploadStatus.CANCELLED
            record["message"] = "Upload cancelled by user"
            record["completed_at"] = datetime.now()
            return True
        
        return False
    
    async def preview_data(self, upload_id: str, rows: int = 10) -> DataPreviewResponse:
        """Preview uploaded data."""
        if upload_id not in self.upload_data:
            raise ValueError(f"Upload {upload_id} not found or data not available")
        
        df = self.upload_data[upload_id]
        
        # Get sample data
        sample_df = df.head(rows)
        sample_data = sample_df.to_dict('records')
        
        # Get data types
        data_types = df.dtypes.astype(str).to_dict()
        
        # Basic statistics
        statistics = {
            "numeric_columns": len(df.select_dtypes(include=['number']).columns),
            "date_columns": len(df.select_dtypes(include=['datetime']).columns),
            "text_columns": len(df.select_dtypes(include=['object']).columns),
            "missing_values": df.isnull().sum().sum()
        }
        
        return DataPreviewResponse(
            columns=df.columns.tolist(),
            sample_data=sample_data,
            total_rows=len(df),
            data_types=data_types,
            statistics=statistics
        )
    
    async def delete_upload(self, upload_id: str) -> bool:
        """Delete an upload record."""
        if upload_id not in self.uploads:
            return False
        
        # Clean up data
        if upload_id in self.upload_data:
            del self.upload_data[upload_id]
        
        del self.uploads[upload_id]
        return True
    
    def _detect_file_format(self, filename: str, content_type: str) -> FileFormat:
        """Detect file format from filename and content type."""
        if filename:
            ext = os.path.splitext(filename)[1].lower()
            if ext == '.csv':
                return FileFormat.CSV
            elif ext in ['.xlsx', '.xls']:
                return FileFormat.EXCEL
            elif ext == '.json':
                return FileFormat.JSON
        
        if content_type:
            if 'csv' in content_type:
                return FileFormat.CSV
            elif 'excel' in content_type or 'spreadsheet' in content_type:
                return FileFormat.EXCEL
            elif 'json' in content_type:
                return FileFormat.JSON
        
        # Default to CSV
        return FileFormat.CSV
    
    async def _parse_file_content(self, content: bytes, file_format: FileFormat, filename: str) -> pd.DataFrame:
        """Parse file content into DataFrame."""
        try:
            if file_format == FileFormat.CSV:
                # Try different encodings
                for encoding in ['utf-8', 'latin-1', 'cp1252']:
                    try:
                        df = pd.read_csv(pd.io.common.BytesIO(content), encoding=encoding)
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    raise ValueError("Unable to decode CSV file")
                    
            elif file_format == FileFormat.EXCEL:
                df = pd.read_excel(pd.io.common.BytesIO(content))
                
            elif file_format == FileFormat.JSON:
                import json
                data = json.loads(content.decode('utf-8'))
                if isinstance(data, list):
                    df = pd.DataFrame(data)
                else:
                    df = pd.DataFrame([data])
            else:
                raise ValueError(f"Unsupported file format: {file_format}")
            
            if df.empty:
                raise ValueError("File contains no data")
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to parse file {filename}: {e}")
            raise ValueError(f"Failed to parse file: {str(e)}")
    
    def _generate_table_name(self, filename: str, data_type: DataType) -> str:
        """Generate table name from filename and data type."""
        # Clean filename
        base_name = os.path.splitext(filename)[0] if filename else "data"
        clean_name = "".join(c if c.isalnum() else "_" for c in base_name.lower())
        
        # Add timestamp to ensure uniqueness
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        return f"{data_type.value}_{clean_name}_{timestamp}"
    
    async def _process_upload_background(self, upload_id: str):
        """Background task to process uploaded data."""
        try:
            record = self.uploads[upload_id]
            df = self.upload_data[upload_id]
            request = record["request"]
            
            # Update status
            record["status"] = UploadStatus.PROCESSING
            record["message"] = "Processing data..."
            record["started_at"] = datetime.now()
            record["progress"] = 10.0
            
            # Process data through pipeline
            if request.validate_data:
                record["message"] = "Validating data quality..."
                record["progress"] = 30.0
                
                pipeline_result = self.data_pipeline.procesar_datos(
                    df, 
                    tipo_datos=request.data_type.value
                )
                
                if not pipeline_result["success"]:
                    raise Exception(f"Data validation failed: {pipeline_result.get('error', 'Unknown error')}")
                
                df_processed = pipeline_result["datos_procesados"]
            else:
                df_processed = df
            
            # Insert into database
            record["message"] = "Inserting data into database..."
            record["progress"] = 70.0
            
            await self._insert_data_to_database(df_processed, record["table_name"], request.overwrite_existing)
            
            # Complete
            record["status"] = UploadStatus.COMPLETED
            record["message"] = f"Data successfully uploaded to table '{record['table_name']}'"
            record["completed_at"] = datetime.now()
            record["progress"] = 100.0
            
            logger.info(f"Upload {upload_id} completed successfully")
            
        except Exception as e:
            logger.error(f"Upload {upload_id} failed: {e}")
            record["status"] = UploadStatus.FAILED
            record["message"] = f"Upload failed: {str(e)}"
            record["completed_at"] = datetime.now()
            record["error_details"] = {"error": str(e), "type": type(e).__name__}
    
    async def _insert_data_to_database(self, df: pd.DataFrame, table_name: str, overwrite: bool):
        """Insert DataFrame into database."""
        try:
            with self.db_config.get_connection() as conn:
                # Check if table exists
                table_exists = conn.execute(
                    f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'"
                ).fetchone() is not None
                
                if table_exists and not overwrite:
                    raise ValueError(f"Table '{table_name}' already exists. Set overwrite_existing=True to replace it.")
                
                # Insert data
                if_exists = 'replace' if overwrite else 'fail'
                df.to_sql(table_name, conn, if_exists=if_exists, index=False)
                
                logger.info(f"Inserted {len(df)} rows into table '{table_name}'")
                
        except Exception as e:
            logger.error(f"Failed to insert data into database: {e}")
            raise
