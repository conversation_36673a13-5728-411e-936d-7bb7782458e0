"""
Pydantic models for data upload functionality.
Defines request/response schemas for file upload and data ingestion endpoints.
"""

from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field, validator
from datetime import datetime
from enum import Enum

class UploadStatus(str, Enum):
    """Upload status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class DataType(str, Enum):
    """Supported data types for upload."""
    FINANCIAL = "financial"
    GENERAL = "general"
    TIME_SERIES = "time_series"
    CUSTOM = "custom"

class FileFormat(str, Enum):
    """Supported file formats."""
    CSV = "csv"
    EXCEL = "excel"
    JSON = "json"

class UploadRequest(BaseModel):
    """Request model for file upload metadata."""
    data_type: DataType = Field(DataType.FINANCIAL, description="Type of data being uploaded")
    table_name: Optional[str] = Field(None, description="Target table name (auto-generated if not provided)")
    description: Optional[str] = Field(None, description="Description of the data")
    overwrite_existing: bool = Field(False, description="Whether to overwrite existing table")
    validate_data: bool = Field(True, description="Whether to validate data quality")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")
    
    @validator('table_name')
    def validate_table_name(cls, v):
        """Validate table name format."""
        if v is not None:
            if not v.replace('_', '').replace('-', '').isalnum():
                raise ValueError('Table name must contain only alphanumeric characters, underscores, and hyphens')
            if len(v) > 50:
                raise ValueError('Table name must be 50 characters or less')
        return v
    
    class Config:
        json_schema_extra = {
            "example": {
                "data_type": "financial",
                "table_name": "stock_prices_2024",
                "description": "Daily stock price data for 2024",
                "overwrite_existing": False,
                "validate_data": True,
                "metadata": {
                    "source": "Yahoo Finance",
                    "frequency": "daily"
                }
            }
        }

class UploadResponse(BaseModel):
    """Response model for file upload."""
    upload_id: str = Field(..., description="Unique upload identifier")
    status: UploadStatus = Field(..., description="Current upload status")
    message: str = Field(..., description="Status message")
    file_info: Dict[str, Any] = Field(..., description="Information about uploaded file")
    table_name: str = Field(..., description="Target table name")
    created_at: datetime = Field(..., description="Upload creation timestamp")
    
    class Config:
        json_schema_extra = {
            "example": {
                "upload_id": "upload_123456789",
                "status": "processing",
                "message": "File uploaded successfully, processing data...",
                "file_info": {
                    "filename": "stock_data.csv",
                    "size": 1024000,
                    "format": "csv",
                    "rows": 5000,
                    "columns": 8
                },
                "table_name": "stock_prices_2024",
                "created_at": "2024-01-01T12:00:00Z"
            }
        }

class UploadStatusResponse(BaseModel):
    """Response model for upload status check."""
    upload_id: str = Field(..., description="Upload identifier")
    status: UploadStatus = Field(..., description="Current status")
    progress: float = Field(..., ge=0, le=100, description="Progress percentage")
    message: str = Field(..., description="Current status message")
    created_at: datetime = Field(..., description="Upload creation timestamp")
    started_at: Optional[datetime] = Field(None, description="Processing start timestamp")
    completed_at: Optional[datetime] = Field(None, description="Completion timestamp")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Error details if failed")
    
    class Config:
        json_schema_extra = {
            "example": {
                "upload_id": "upload_123456789",
                "status": "completed",
                "progress": 100.0,
                "message": "Data uploaded and processed successfully",
                "created_at": "2024-01-01T12:00:00Z",
                "started_at": "2024-01-01T12:00:05Z",
                "completed_at": "2024-01-01T12:01:30Z",
                "error_details": None
            }
        }

class DataValidationResult(BaseModel):
    """Model for data validation results."""
    is_valid: bool = Field(..., description="Whether data passed validation")
    quality_score: float = Field(..., ge=0, le=100, description="Data quality score")
    issues: List[Dict[str, Any]] = Field(default_factory=list, description="Validation issues found")
    recommendations: List[str] = Field(default_factory=list, description="Improvement recommendations")
    statistics: Dict[str, Any] = Field(default_factory=dict, description="Data statistics")
    
    class Config:
        json_schema_extra = {
            "example": {
                "is_valid": True,
                "quality_score": 85.5,
                "issues": [
                    {
                        "type": "missing_values",
                        "column": "volume",
                        "count": 12,
                        "severity": "low"
                    }
                ],
                "recommendations": [
                    "Consider imputation for missing volume values"
                ],
                "statistics": {
                    "total_rows": 5000,
                    "total_columns": 8,
                    "missing_values": 12,
                    "duplicates": 0
                }
            }
        }

class ProcessingResult(BaseModel):
    """Model for data processing results."""
    success: bool = Field(..., description="Whether processing was successful")
    table_name: str = Field(..., description="Target table name")
    rows_processed: int = Field(..., description="Number of rows processed")
    rows_inserted: int = Field(..., description="Number of rows inserted")
    processing_time: float = Field(..., description="Processing time in seconds")
    validation_result: Optional[DataValidationResult] = Field(None, description="Validation results")
    transformations_applied: List[str] = Field(default_factory=list, description="Data transformations applied")
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "table_name": "stock_prices_2024",
                "rows_processed": 5000,
                "rows_inserted": 4988,
                "processing_time": 12.5,
                "validation_result": {
                    "is_valid": True,
                    "quality_score": 85.5,
                    "issues": [],
                    "recommendations": [],
                    "statistics": {}
                },
                "transformations_applied": [
                    "Converted date column to datetime",
                    "Forward filled missing volume values"
                ]
            }
        }

class UploadListResponse(BaseModel):
    """Response model for listing uploads."""
    uploads: List[UploadStatusResponse] = Field(..., description="List of uploads")
    total: int = Field(..., description="Total number of uploads")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of items per page")
    
    class Config:
        json_schema_extra = {
            "example": {
                "uploads": [],
                "total": 25,
                "page": 1,
                "page_size": 10
            }
        }

class DataPreviewResponse(BaseModel):
    """Response model for data preview."""
    columns: List[str] = Field(..., description="Column names")
    sample_data: List[Dict[str, Any]] = Field(..., description="Sample rows")
    total_rows: int = Field(..., description="Total number of rows")
    data_types: Dict[str, str] = Field(..., description="Detected data types")
    statistics: Dict[str, Any] = Field(default_factory=dict, description="Basic statistics")
    
    class Config:
        json_schema_extra = {
            "example": {
                "columns": ["date", "open", "high", "low", "close", "volume"],
                "sample_data": [
                    {
                        "date": "2024-01-01",
                        "open": 100.0,
                        "high": 105.0,
                        "low": 98.0,
                        "close": 103.0,
                        "volume": 1000000
                    }
                ],
                "total_rows": 5000,
                "data_types": {
                    "date": "datetime",
                    "open": "float",
                    "high": "float",
                    "low": "float",
                    "close": "float",
                    "volume": "integer"
                },
                "statistics": {
                    "numeric_columns": 5,
                    "date_columns": 1,
                    "missing_values": 12
                }
            }
        }
