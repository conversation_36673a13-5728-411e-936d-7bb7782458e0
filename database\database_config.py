"""
Database configuration and connection management for the financial analyst system.
Provides secure database connections with connection pooling and error handling.
"""

import os
import logging
from typing import Optional, Dict, Any
from sqlalchemy import create_engine, text, MetaData, inspect
from sqlalchemy.engine import Engine
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import SQLAlchemyError
from contextlib import contextmanager
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseConfig:
    """Database configuration and connection management."""
    
    def __init__(self):
        self.engine: Optional[Engine] = None
        self.metadata: Optional[MetaData] = None
        self._connection_params = self._load_connection_params()
    
    def _load_connection_params(self) -> Dict[str, Any]:
        """Load database connection parameters from environment variables."""
        return {
            'database_url': os.getenv('DATABASE_URL', 'sqlite:///financial_data.db'),
            'pool_size': int(os.getenv('DB_POOL_SIZE', '5')),
            'max_overflow': int(os.getenv('DB_MAX_OVERFLOW', '10')),
            'pool_timeout': int(os.getenv('DB_POOL_TIMEOUT', '30')),
            'pool_recycle': int(os.getenv('DB_POOL_RECYCLE', '3600')),
            'echo': os.getenv('DB_ECHO', 'False').lower() == 'true'
        }
    
    def initialize_engine(self) -> Engine:
        """Initialize database engine with connection pooling."""
        try:
            if self.engine is None:
                self.engine = create_engine(
                    self._connection_params['database_url'],
                    poolclass=QueuePool,
                    pool_size=self._connection_params['pool_size'],
                    max_overflow=self._connection_params['max_overflow'],
                    pool_timeout=self._connection_params['pool_timeout'],
                    pool_recycle=self._connection_params['pool_recycle'],
                    echo=self._connection_params['echo']
                )
                
                # Initialize metadata
                self.metadata = MetaData()
                self.metadata.reflect(bind=self.engine)
                
                logger.info("Database engine initialized successfully")
            
            return self.engine
            
        except SQLAlchemyError as e:
            logger.error(f"Failed to initialize database engine: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Context manager for database connections."""
        if self.engine is None:
            self.initialize_engine()
        
        connection = None
        try:
            connection = self.engine.connect()
            yield connection
        except SQLAlchemyError as e:
            logger.error(f"Database connection error: {e}")
            if connection:
                connection.rollback()
            raise
        finally:
            if connection:
                connection.close()
    
    def test_connection(self) -> bool:
        """Test database connection."""
        try:
            with self.get_connection() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
                logger.info("Database connection test successful")
                return True
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False
    
    def get_table_names(self) -> list:
        """Get list of table names in the database."""
        try:
            if self.engine is None:
                self.initialize_engine()
            
            inspector = inspect(self.engine)
            tables = inspector.get_table_names()
            logger.info(f"Found {len(tables)} tables in database")
            return tables
            
        except SQLAlchemyError as e:
            logger.error(f"Failed to get table names: {e}")
            return []
    
    def get_table_schema(self, table_name: str) -> Dict[str, Any]:
        """Get schema information for a specific table."""
        try:
            if self.engine is None:
                self.initialize_engine()
            
            inspector = inspect(self.engine)
            columns = inspector.get_columns(table_name)
            primary_keys = inspector.get_pk_constraint(table_name)
            foreign_keys = inspector.get_foreign_keys(table_name)
            indexes = inspector.get_indexes(table_name)
            
            schema_info = {
                'table_name': table_name,
                'columns': columns,
                'primary_keys': primary_keys,
                'foreign_keys': foreign_keys,
                'indexes': indexes
            }
            
            logger.info(f"Retrieved schema for table: {table_name}")
            return schema_info
            
        except SQLAlchemyError as e:
            logger.error(f"Failed to get schema for table {table_name}: {e}")
            return {}
    
    def execute_query(self, query: str, params: Optional[Dict] = None) -> list:
        """Execute a SQL query safely with parameters."""
        try:
            with self.get_connection() as conn:
                result = conn.execute(text(query), params or {})
                rows = result.fetchall()
                
                # Convert to list of dictionaries
                columns = result.keys()
                data = [dict(zip(columns, row)) for row in rows]
                
                logger.info(f"Query executed successfully, returned {len(data)} rows")
                return data
                
        except SQLAlchemyError as e:
            logger.error(f"Query execution failed: {e}")
            raise
    
    def close_engine(self):
        """Close database engine and all connections."""
        if self.engine:
            self.engine.dispose()
            self.engine = None
            logger.info("Database engine closed")

# Global database configuration instance
db_config = DatabaseConfig()

def get_database_config() -> DatabaseConfig:
    """Get the global database configuration instance."""
    return db_config
