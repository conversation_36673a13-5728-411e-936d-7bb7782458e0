"""
System management endpoints for configuration and administration.
"""

import logging
import os
import psutil
from typing import Dict, Any, List
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import JSONResponse

from api.models.common_models import (
    SuccessResponse, SystemStatusResponse, SystemMetrics, HealthStatus
)
from api.dependencies import (
    get_settings_dependency, get_database_config_dependency,
    get_agent_coordinator_dependency
)
from config.settings import Settings
from database.database_config import DatabaseConfig
from agents.agent_coordinator import CoordinadorAgentes

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/info", response_model=SuccessResponse, status_code=status.HTTP_200_OK)
async def get_system_info(
    settings: Settings = Depends(get_settings_dependency)
):
    """
    Get basic system information and configuration.
    
    Returns general information about the API including:
    - Version information
    - Configuration summary
    - Available endpoints
    - System capabilities
    """
    try:
        system_info = {
            "api_version": "1.0.0",
            "python_version": f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}",
            "environment": "development" if settings.app.debug else "production",
            "features": {
                "workflows": True,
                "sql_agent": True,
                "pandas_agent": True,
                "schema_analyzer": True,
                "batch_processing": True,
                "rate_limiting": True,
                "health_checks": True
            },
            "endpoints": {
                "workflows": "/workflows",
                "agents": "/agents", 
                "health": "/health",
                "system": "/system",
                "docs": "/docs",
                "redoc": "/redoc"
            },
            "limits": {
                "max_query_length": settings.security.max_query_length,
                "rate_limit_per_minute": settings.security.rate_limit_per_minute,
                "max_concurrent_workflows": settings.app.max_concurrent_workflows,
                "session_timeout": settings.app.session_timeout
            }
        }
        
        return SuccessResponse(
            message="System information retrieved successfully",
            data=system_info
        )
        
    except Exception as e:
        logger.error(f"Failed to get system info: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve system information"
        )


@router.get("/metrics", response_model=SystemStatusResponse, status_code=status.HTTP_200_OK)
async def get_system_metrics(
    coordinator: CoordinadorAgentes = Depends(get_agent_coordinator_dependency),
    db_config: DatabaseConfig = Depends(get_database_config_dependency)
):
    """
    Get detailed system performance metrics.
    
    Returns comprehensive system metrics including:
    - Workflow statistics
    - Agent status
    - Database performance
    - System resource usage
    """
    try:
        # Calculate workflow metrics
        total_workflows = len(coordinator.workflows)
        active_workflows = len([w for w in coordinator.workflows.values() 
                              if w.status.value in ["pending", "in_progress"]])
        completed_workflows = len([w for w in coordinator.workflows.values() 
                                 if w.status.value == "completed"])
        failed_workflows = len([w for w in coordinator.workflows.values() 
                              if w.status.value == "failed"])
        
        # Get system resource usage
        try:
            memory_usage = psutil.virtual_memory().percent
            cpu_usage = psutil.cpu_percent(interval=1)
        except:
            memory_usage = None
            cpu_usage = None
        
        # Check database status
        db_status = HealthStatus.HEALTHY if db_config.test_connection() else HealthStatus.UNHEALTHY
        
        # Check agent status
        agent_status = {}
        for agent_type in coordinator.agent_registry.keys():
            try:
                # Basic agent availability check
                agent_status[agent_type.value] = HealthStatus.HEALTHY
            except:
                agent_status[agent_type.value] = HealthStatus.UNHEALTHY
        
        # Create metrics
        metrics = SystemMetrics(
            active_workflows=active_workflows,
            total_requests=total_workflows,  # Using workflows as proxy for requests
            average_response_time=0.0,  # Would be calculated from logs/metrics
            error_rate=failed_workflows / max(total_workflows, 1) * 100,
            memory_usage=memory_usage,
            cpu_usage=cpu_usage
        )
        
        return SystemStatusResponse(
            message="System metrics retrieved successfully",
            metrics=metrics,
            database_status=db_status,
            agent_status=agent_status
        )
        
    except Exception as e:
        logger.error(f"Failed to get system metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve system metrics"
        )


@router.get("/config", response_model=SuccessResponse, status_code=status.HTTP_200_OK)
async def get_system_config(
    settings: Settings = Depends(get_settings_dependency)
):
    """
    Get current system configuration (non-sensitive values only).
    
    Returns configuration information that is safe to expose:
    - Database settings (without credentials)
    - Application settings
    - Feature flags
    - Limits and timeouts
    """
    try:
        # Create safe configuration dict (excluding sensitive information)
        safe_config = {
            "database": {
                "pool_size": settings.database.pool_size,
                "max_overflow": settings.database.max_overflow,
                "pool_timeout": settings.database.pool_timeout,
                "pool_recycle": settings.database.pool_recycle,
                "echo": settings.database.echo
            },
            "application": {
                "debug": settings.app.debug,
                "log_level": settings.app.log_level,
                "session_timeout": settings.app.session_timeout,
                "max_concurrent_workflows": settings.app.max_concurrent_workflows,
                "enable_caching": settings.app.enable_caching
            },
            "dashboard": {
                "default_theme": settings.dashboard.default_theme,
                "auto_refresh": settings.dashboard.auto_refresh,
                "refresh_interval": settings.dashboard.refresh_interval,
                "max_charts": settings.dashboard.max_charts,
                "default_layout": settings.dashboard.default_layout
            },
            "security": {
                "enable_auth": settings.security.enable_auth,
                "max_query_length": settings.security.max_query_length,
                "rate_limit_per_minute": settings.security.rate_limit_per_minute
            },
            "llm": {
                "default_model": settings.llm.default_model,
                "temperature": settings.llm.temperature,
                "max_tokens": settings.llm.max_tokens
            }
        }
        
        return SuccessResponse(
            message="System configuration retrieved successfully",
            data=safe_config
        )
        
    except Exception as e:
        logger.error(f"Failed to get system config: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve system configuration"
        )


@router.get("/database/tables", response_model=SuccessResponse, status_code=status.HTTP_200_OK)
async def get_database_tables(
    db_config: DatabaseConfig = Depends(get_database_config_dependency)
):
    """
    Get list of available database tables.
    
    Returns information about database tables including:
    - Table names
    - Table count
    - Basic schema information
    """
    try:
        tables = db_config.get_table_names()
        
        table_info = {
            "tables": tables,
            "table_count": len(tables),
            "retrieved_at": datetime.now().isoformat()
        }
        
        return SuccessResponse(
            message=f"Retrieved {len(tables)} database tables",
            data=table_info
        )
        
    except Exception as e:
        logger.error(f"Failed to get database tables: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve database tables"
        )


@router.get("/database/schema/{table_name}", response_model=SuccessResponse, status_code=status.HTTP_200_OK)
async def get_table_schema(
    table_name: str,
    db_config: DatabaseConfig = Depends(get_database_config_dependency)
):
    """
    Get schema information for a specific table.
    
    Returns detailed schema information including:
    - Column definitions
    - Primary keys
    - Foreign keys
    - Indexes
    """
    try:
        schema_info = db_config.get_table_schema(table_name)
        
        if not schema_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Table '{table_name}' not found"
            )
        
        return SuccessResponse(
            message=f"Schema information retrieved for table '{table_name}'",
            data=schema_info
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get table schema for {table_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve table schema"
        )


@router.get("/logs", response_model=SuccessResponse, status_code=status.HTTP_200_OK)
async def get_recent_logs(
    lines: int = 100,
    level: str = "INFO"
):
    """
    Get recent log entries (if log files are accessible).
    
    Returns recent log entries for debugging and monitoring.
    
    - **lines**: Number of recent log lines to retrieve
    - **level**: Minimum log level to include
    """
    try:
        log_file_path = "logs/api.log"
        
        if not os.path.exists(log_file_path):
            return SuccessResponse(
                message="Log file not found",
                data={"logs": [], "message": "Log file does not exist"}
            )
        
        # Read recent log lines
        with open(log_file_path, 'r') as f:
            all_lines = f.readlines()
            recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
        
        # Filter by log level if specified
        if level != "ALL":
            filtered_lines = [line for line in recent_lines if level in line]
        else:
            filtered_lines = recent_lines
        
        log_data = {
            "logs": [line.strip() for line in filtered_lines],
            "total_lines": len(filtered_lines),
            "log_file": log_file_path,
            "retrieved_at": datetime.now().isoformat()
        }
        
        return SuccessResponse(
            message=f"Retrieved {len(filtered_lines)} log entries",
            data=log_data
        )
        
    except Exception as e:
        logger.error(f"Failed to get recent logs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve log entries"
        )


@router.post("/cleanup", response_model=SuccessResponse, status_code=status.HTTP_200_OK)
async def cleanup_system(
    coordinator: CoordinadorAgentes = Depends(get_agent_coordinator_dependency)
):
    """
    Perform system cleanup operations.
    
    Cleans up completed workflows, temporary files, and other system resources.
    """
    try:
        cleanup_results = {
            "workflows_cleaned": 0,
            "temp_files_cleaned": 0,
            "memory_freed": 0
        }
        
        # Clean up completed workflows older than 24 hours
        current_time = datetime.now()
        workflows_to_remove = []
        
        for workflow_id, workflow in coordinator.workflows.items():
            if (workflow.status.value in ["completed", "failed", "cancelled"] and 
                workflow.updated_at and 
                (current_time - workflow.updated_at).total_seconds() > 86400):  # 24 hours
                workflows_to_remove.append(workflow_id)
        
        for workflow_id in workflows_to_remove:
            del coordinator.workflows[workflow_id]
            cleanup_results["workflows_cleaned"] += 1
        
        logger.info(f"System cleanup completed: {cleanup_results}")
        
        return SuccessResponse(
            message="System cleanup completed successfully",
            data=cleanup_results
        )
        
    except Exception as e:
        logger.error(f"Failed to perform system cleanup: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform system cleanup"
        )
