# 🤖 Database Analyst System

A comprehensive multi-agent system that automates dashboard creation and enables dynamic interaction with databases via an intelligent chatbot interface. Built with LangGraph, Streamlit, and advanced AI agents.

## 🌟 Features

### 🔍 Multi-Agent Architecture
- **SQL Agent**: Natural language to SQL query translation with security validation
- **Enhanced Pandas Agent**: Advanced data analysis with statistical insights and visualizations
- **Schema Analyzer**: Intelligent database schema analysis and optimization recommendations
- **Dashboard Generator**: Automated creation of interactive dashboards
- **Agent Coordinator**: Orchestrates workflow execution and inter-agent communication

### 💬 Interactive Chatbot Interface
- Natural language query processing
- Real-time conversation with context awareness
- Voice input support (coming soon)
- Export chat history and results

### 📊 Automated Dashboard Creation
- Intelligent chart selection based on data patterns
- Interactive Plotly visualizations
- Multiple layout options (single chart, grid, custom)
- Real-time data updates
- Export capabilities

### 🔒 Security & Performance
- SQL injection prevention
- Query validation and sanitization
- Connection pooling and caching
- Rate limiting and session management
- Comprehensive error handling

## 🏗️ Architecture

```
financial_analyst/
├── agents/                 # AI Agents
│   ├── sql_agent.py       # SQL query agent
│   ├── enhanced_pandas_agent.py  # Advanced data analysis
│   ├── agent_coordinator.py      # Workflow orchestration
│   └── schema_analyzer.py        # Database schema analysis
├── dashboard/             # Dashboard Components
│   ├── dashboard_generator.py    # Dashboard creation
│   └── chart_factory.py         # Advanced chart generation
├── database/              # Database Management
│   └── database_config.py       # Connection management
├── utils/                 # Utilities
│   └── data_pipeline.py         # Data processing pipeline
├── config/                # Configuration
│   └── settings.py              # Application settings
├── tests/                 # Test Suite
├── main_app.py           # Streamlit application
├── example.py            # Original + enhanced functionality
└── requirements.txt      # Dependencies
```

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd financial_analyst

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your settings
# Required: GROQ_API_KEY
```

### 3. Run the Application

#### Streamlit Web Interface (Recommended)
```bash
streamlit run main_app.py
```

#### Command Line Interface
```bash
# Original functionality
python example.py

# Enhanced multi-agent system
python example.py --advanced "analyze sales data trends"
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `GROQ_API_KEY` | Groq API key for LLM access | Required |
| `DATABASE_URL` | Database connection string | `sqlite:///financial_data.db` |
| `DASHBOARD_THEME` | Default dashboard theme | `plotly_white` |
| `LOG_LEVEL` | Logging level | `INFO` |

### Database Support

- **SQLite** (default): Local development
- **PostgreSQL**: Production databases
- **MySQL**: Enterprise databases
- **SQL Server**: Microsoft environments

## 📖 Usage Examples

### Natural Language Queries

```python
# Financial analysis
"Show me the stock performance for AAPL over the last quarter"

# Database queries
"What are the top 10 customers by revenue?"

# Dashboard creation
"Create a dashboard showing sales trends and customer analytics"

# Schema analysis
"Analyze the database schema and suggest optimizations"
```

### Programmatic Usage

```python
from agents import get_agent_coordinator
from dashboard import GeneradorDashboard

# Initialize coordinator
coordinator = get_agent_coordinator()

# Create workflow
workflow = coordinator.crear_workflow("analysis_1", "analyze customer data")
result = coordinator.ejecutar_workflow("analysis_1")

# Generate dashboard
dashboard_gen = GeneradorDashboard()
dashboard = dashboard_gen.generar_dashboard_automatico(result["data"])
```

## 🧪 Testing

```bash
# Run all tests
pytest tests/

# Run specific test category
pytest tests/test_agents.py
pytest tests/test_dashboard.py
pytest tests/test_database.py
```

## 📊 Dashboard Features

### Chart Types
- **Time Series**: Line, area, candlestick charts
- **Categorical**: Bar, pie, donut charts
- **Statistical**: Histogram, box plots, violin plots
- **Correlation**: Heatmaps, scatter matrices
- **Advanced**: Bubble charts, treemaps, radar charts

### Layout Options
- Single chart view
- Two-column layout
- Grid layouts (2x2, 3x3)
- Custom responsive layouts

### Interactive Features
- Zoom, pan, and hover interactions
- Real-time data updates
- Export to PNG, PDF, HTML
- Responsive design for mobile

## 🔐 Security Features

### SQL Security
- Parameterized queries
- SQL injection prevention
- Query validation and sanitization
- Access control and permissions

### Application Security
- Session management
- Rate limiting
- Input validation
- Secure configuration management

## 🚀 Performance Optimization

### Database
- Connection pooling
- Query optimization
- Result caching
- Lazy loading

### Application
- Streamlit caching
- Async processing
- Memory management
- Resource cleanup

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Setup

```bash
# Install development dependencies
pip install -r requirements.txt
pip install pytest black flake8

# Run code formatting
black .

# Run linting
flake8 .

# Run tests
pytest
```

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **LangChain/LangGraph**: Multi-agent framework
- **Streamlit**: Web application framework
- **Plotly**: Interactive visualizations
- **Groq**: High-performance LLM inference
- **SQLAlchemy**: Database toolkit

## 📞 Support

For support, please open an issue on GitHub or contact the development team.

## 🗺️ Roadmap

- [ ] Voice input integration
- [ ] Advanced ML model integration
- [ ] Real-time collaboration features
- [ ] Mobile application
- [ ] Cloud deployment templates
- [ ] Advanced security features
- [ ] Multi-language support

---

**Built with ❤️ by the Arroyo Consulting Team**
