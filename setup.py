"""
Setup script for the Database Analyst System.
Handles installation, configuration, and initial setup.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")

def install_dependencies():
    """Install required dependencies."""
    print("📦 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies")
        sys.exit(1)

def setup_environment():
    """Setup environment configuration."""
    print("🔧 Setting up environment...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        shutil.copy(env_example, env_file)
        print("✅ Environment file created from template")
        print("⚠️  Please edit .env file with your configuration")
    elif env_file.exists():
        print("✅ Environment file already exists")
    else:
        print("❌ No environment template found")

def create_directories():
    """Create necessary directories."""
    print("📁 Creating directories...")
    
    directories = [
        "logs",
        "data",
        "exports",
        "cache"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ Directories created")

def test_installation():
    """Test the installation."""
    print("🧪 Testing installation...")
    
    try:
        # Test imports
        import streamlit
        import plotly
        import pandas
        import sqlalchemy
        import langchain
        import langgraph
        
        print("✅ All core packages imported successfully")
        
        # Test database connection
        from database.database_config import get_database_config
        db_config = get_database_config()
        
        if db_config.test_connection():
            print("✅ Database connection test passed")
        else:
            print("⚠️  Database connection test failed (this is normal for first setup)")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"⚠️  Test warning: {e}")
        return True

def display_next_steps():
    """Display next steps for the user."""
    print("\n" + "="*50)
    print("🎉 Setup completed successfully!")
    print("="*50)
    print("\n📋 Next Steps:")
    print("1. Edit .env file with your GROQ_API_KEY")
    print("2. Configure database settings if needed")
    print("3. Run the application:")
    print("   • Web interface: streamlit run main_app.py")
    print("   • Command line: python example.py")
    print("   • Advanced mode: python example.py --advanced 'your query'")
    print("\n📚 Documentation:")
    print("   • README.md for detailed usage instructions")
    print("   • Check the agents/ folder for individual agent documentation")
    print("\n🆘 Support:")
    print("   • Open an issue on GitHub for problems")
    print("   • Check logs/ folder for error details")

def main():
    """Main setup function."""
    print("🚀 Database Analyst System Setup")
    print("="*40)
    
    # Check requirements
    check_python_version()
    
    # Install dependencies
    install_dependencies()
    
    # Setup environment
    setup_environment()
    
    # Create directories
    create_directories()
    
    # Test installation
    if test_installation():
        display_next_steps()
    else:
        print("❌ Setup completed with errors. Please check the output above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
