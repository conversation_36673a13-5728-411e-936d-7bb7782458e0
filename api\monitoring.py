"""
Monitoring and metrics collection for the FastAPI application.
Provides comprehensive monitoring of API performance and system health.
"""

import logging
import time
import psutil
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from collections import defaultdict, deque
from dataclasses import dataclass, field
import threading
import json

logger = logging.getLogger(__name__)


@dataclass
class RequestMetrics:
    """Metrics for individual requests."""
    timestamp: datetime
    method: str
    endpoint: str
    status_code: int
    response_time: float
    request_size: int = 0
    response_size: int = 0
    user_agent: str = ""
    client_ip: str = ""


@dataclass
class SystemMetrics:
    """System performance metrics."""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    disk_usage_percent: float
    active_connections: int = 0
    total_requests: int = 0
    error_count: int = 0


class MetricsCollector:
    """Collects and stores application metrics."""
    
    def __init__(self, max_history: int = 10000):
        self.max_history = max_history
        self.request_metrics: deque = deque(maxlen=max_history)
        self.system_metrics: deque = deque(maxlen=1000)  # Keep less system metrics
        self.endpoint_stats: Dict[str, Dict[str, Any]] = defaultdict(lambda: {
            "count": 0,
            "total_time": 0.0,
            "min_time": float('inf'),
            "max_time": 0.0,
            "error_count": 0,
            "last_accessed": None
        })
        self.error_log: deque = deque(maxlen=1000)
        self.lock = threading.Lock()
        
        # Start system metrics collection
        self._start_system_monitoring()
    
    def record_request(self, metrics: RequestMetrics):
        """Record request metrics."""
        with self.lock:
            self.request_metrics.append(metrics)
            
            # Update endpoint statistics
            endpoint_key = f"{metrics.method} {metrics.endpoint}"
            stats = self.endpoint_stats[endpoint_key]
            
            stats["count"] += 1
            stats["total_time"] += metrics.response_time
            stats["min_time"] = min(stats["min_time"], metrics.response_time)
            stats["max_time"] = max(stats["max_time"], metrics.response_time)
            stats["last_accessed"] = metrics.timestamp
            
            if metrics.status_code >= 400:
                stats["error_count"] += 1
                
                # Record error details
                self.error_log.append({
                    "timestamp": metrics.timestamp,
                    "method": metrics.method,
                    "endpoint": metrics.endpoint,
                    "status_code": metrics.status_code,
                    "client_ip": metrics.client_ip,
                    "user_agent": metrics.user_agent
                })
    
    def get_request_stats(self, time_window: Optional[timedelta] = None) -> Dict[str, Any]:
        """Get request statistics for a time window."""
        with self.lock:
            if time_window:
                cutoff_time = datetime.now() - time_window
                filtered_metrics = [m for m in self.request_metrics if m.timestamp >= cutoff_time]
            else:
                filtered_metrics = list(self.request_metrics)
            
            if not filtered_metrics:
                return {
                    "total_requests": 0,
                    "average_response_time": 0.0,
                    "error_rate": 0.0,
                    "requests_per_minute": 0.0
                }
            
            total_requests = len(filtered_metrics)
            total_time = sum(m.response_time for m in filtered_metrics)
            error_count = sum(1 for m in filtered_metrics if m.status_code >= 400)
            
            # Calculate requests per minute
            if time_window:
                minutes = time_window.total_seconds() / 60
                requests_per_minute = total_requests / minutes if minutes > 0 else 0
            else:
                # Use actual time span of data
                if len(filtered_metrics) > 1:
                    time_span = (filtered_metrics[-1].timestamp - filtered_metrics[0].timestamp).total_seconds() / 60
                    requests_per_minute = total_requests / time_span if time_span > 0 else 0
                else:
                    requests_per_minute = 0
            
            return {
                "total_requests": total_requests,
                "average_response_time": total_time / total_requests,
                "error_rate": (error_count / total_requests) * 100,
                "requests_per_minute": requests_per_minute,
                "error_count": error_count
            }
    
    def get_endpoint_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all endpoints."""
        with self.lock:
            stats = {}
            for endpoint, data in self.endpoint_stats.items():
                if data["count"] > 0:
                    stats[endpoint] = {
                        "count": data["count"],
                        "average_time": data["total_time"] / data["count"],
                        "min_time": data["min_time"],
                        "max_time": data["max_time"],
                        "error_count": data["error_count"],
                        "error_rate": (data["error_count"] / data["count"]) * 100,
                        "last_accessed": data["last_accessed"].isoformat() if data["last_accessed"] else None
                    }
            return stats
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get current system statistics."""
        try:
            current_metrics = SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=psutil.cpu_percent(interval=0.1),
                memory_percent=psutil.virtual_memory().percent,
                disk_usage_percent=psutil.disk_usage('/').percent,
                active_connections=len(psutil.net_connections()),
                total_requests=len(self.request_metrics),
                error_count=len(self.error_log)
            )
            
            with self.lock:
                self.system_metrics.append(current_metrics)
            
            return {
                "cpu_percent": current_metrics.cpu_percent,
                "memory_percent": current_metrics.memory_percent,
                "disk_usage_percent": current_metrics.disk_usage_percent,
                "active_connections": current_metrics.active_connections,
                "total_requests": current_metrics.total_requests,
                "error_count": current_metrics.error_count,
                "timestamp": current_metrics.timestamp.isoformat()
            }
        except Exception as e:
            logger.error(f"Failed to get system stats: {e}")
            return {
                "cpu_percent": 0.0,
                "memory_percent": 0.0,
                "disk_usage_percent": 0.0,
                "active_connections": 0,
                "total_requests": len(self.request_metrics),
                "error_count": len(self.error_log),
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
    
    def get_recent_errors(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent error entries."""
        with self.lock:
            recent_errors = list(self.error_log)[-limit:]
            return [
                {
                    "timestamp": error["timestamp"].isoformat(),
                    "method": error["method"],
                    "endpoint": error["endpoint"],
                    "status_code": error["status_code"],
                    "client_ip": error["client_ip"],
                    "user_agent": error["user_agent"]
                }
                for error in recent_errors
            ]
    
    def _start_system_monitoring(self):
        """Start background system monitoring."""
        def monitor():
            while True:
                try:
                    self.get_system_stats()  # This will record the metrics
                    time.sleep(60)  # Collect system metrics every minute
                except Exception as e:
                    logger.error(f"System monitoring error: {e}")
                    time.sleep(60)
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
        logger.info("System monitoring started")
    
    def export_metrics(self, format: str = "json") -> str:
        """Export all metrics in specified format."""
        with self.lock:
            data = {
                "export_timestamp": datetime.now().isoformat(),
                "request_stats": self.get_request_stats(),
                "endpoint_stats": self.get_endpoint_stats(),
                "system_stats": self.get_system_stats(),
                "recent_errors": self.get_recent_errors(),
                "total_metrics_collected": len(self.request_metrics)
            }
            
            if format.lower() == "json":
                return json.dumps(data, indent=2, default=str)
            else:
                return str(data)


class PerformanceMonitor:
    """Context manager for monitoring operation performance."""
    
    def __init__(self, operation_name: str, logger: Optional[logging.Logger] = None):
        self.operation_name = operation_name
        self.logger = logger or logging.getLogger(__name__)
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        self.logger.debug(f"Starting operation: {self.operation_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        duration = self.end_time - self.start_time
        
        if exc_type is None:
            self.logger.info(f"Operation '{self.operation_name}' completed in {duration:.3f}s")
        else:
            self.logger.error(f"Operation '{self.operation_name}' failed after {duration:.3f}s: {exc_val}")
    
    @property
    def duration(self) -> Optional[float]:
        """Get operation duration if completed."""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None


# Global metrics collector instance
_metrics_collector: Optional[MetricsCollector] = None


def get_metrics_collector() -> MetricsCollector:
    """Get the global metrics collector instance."""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = MetricsCollector()
    return _metrics_collector


def setup_monitoring():
    """Initialize monitoring system."""
    collector = get_metrics_collector()
    logger.info("Monitoring system initialized")
    return collector


# Export monitoring utilities
__all__ = [
    "RequestMetrics",
    "SystemMetrics", 
    "MetricsCollector",
    "PerformanceMonitor",
    "get_metrics_collector",
    "setup_monitoring"
]
