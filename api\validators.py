"""
Custom validators and validation utilities for API requests.
"""

import re
import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from pydantic import validator, ValidationError
from fastapi import HTTPException, status

logger = logging.getLogger(__name__)


class ValidationUtils:
    """Utility class for common validation operations."""
    
    # SQL injection patterns to detect
    SQL_INJECTION_PATTERNS = [
        r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE)\b)",
        r"(--|#|/\*|\*/)",
        r"(\b(UNION|OR|AND)\b.*\b(SELECT|INSERT|UPDATE|DELETE)\b)",
        r"(\b(SCRIPT|JAVASCRIPT|VBSCRIPT)\b)",
        r"(<script|</script>|<iframe|</iframe>)",
    ]
    
    # Allowed SQL keywords for read-only operations
    ALLOWED_SQL_KEYWORDS = [
        "SELECT", "FROM", "WHERE", "JOIN", "INNER", "LEFT", "RIGHT", "OUTER",
        "GROUP", "BY", "ORDER", "HAVING", "LIMIT", "OFFSET", "AS", "DISTINCT",
        "COUNT", "SUM", "AVG", "MIN", "MAX", "CASE", "WHEN", "THEN", "ELSE", "END"
    ]
    
    @staticmethod
    def validate_sql_query(query: str, allow_write_operations: bool = False) -> bool:
        """
        Validate SQL query for security and allowed operations.
        
        Args:
            query: SQL query to validate
            allow_write_operations: Whether to allow write operations
            
        Returns:
            bool: True if query is valid and safe
            
        Raises:
            ValueError: If query contains dangerous patterns
        """
        if not query or not query.strip():
            raise ValueError("SQL query cannot be empty")
        
        query_upper = query.upper().strip()
        
        # Check for dangerous patterns
        for pattern in ValidationUtils.SQL_INJECTION_PATTERNS:
            if re.search(pattern, query_upper, re.IGNORECASE):
                # Allow SELECT statements even if they match some patterns
                if not query_upper.startswith("SELECT"):
                    raise ValueError(f"Potentially dangerous SQL pattern detected")
        
        # Check for write operations if not allowed
        if not allow_write_operations:
            dangerous_keywords = [
                "INSERT", "UPDATE", "DELETE", "DROP", "CREATE", "ALTER", 
                "TRUNCATE", "REPLACE", "MERGE", "EXEC", "EXECUTE"
            ]
            
            for keyword in dangerous_keywords:
                if re.search(rf"\b{keyword}\b", query_upper):
                    raise ValueError(f"Write operation '{keyword}' not allowed in read-only mode")
        
        return True
    
    @staticmethod
    def validate_natural_language_query(query: str) -> bool:
        """
        Validate natural language query for basic safety.
        
        Args:
            query: Natural language query to validate
            
        Returns:
            bool: True if query is valid
            
        Raises:
            ValueError: If query contains dangerous content
        """
        if not query or not query.strip():
            raise ValueError("Query cannot be empty")
        
        if len(query) > 10000:
            raise ValueError("Query too long (maximum 10,000 characters)")
        
        # Check for script injection attempts
        dangerous_patterns = [
            r"<script.*?>.*?</script>",
            r"javascript:",
            r"vbscript:",
            r"on\w+\s*=",
            r"eval\s*\(",
            r"exec\s*\(",
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                raise ValueError("Potentially dangerous content detected in query")
        
        return True
    
    @staticmethod
    def validate_workflow_id(workflow_id: str) -> bool:
        """
        Validate workflow ID format.
        
        Args:
            workflow_id: Workflow ID to validate
            
        Returns:
            bool: True if valid
            
        Raises:
            ValueError: If workflow ID is invalid
        """
        if not workflow_id:
            raise ValueError("Workflow ID cannot be empty")
        
        if len(workflow_id) < 5 or len(workflow_id) > 50:
            raise ValueError("Workflow ID must be between 5 and 50 characters")
        
        # Allow alphanumeric characters, hyphens, and underscores
        if not re.match(r"^[a-zA-Z0-9_-]+$", workflow_id):
            raise ValueError("Workflow ID can only contain letters, numbers, hyphens, and underscores")
        
        return True
    
    @staticmethod
    def validate_agent_type(agent_type: str) -> bool:
        """
        Validate agent type.
        
        Args:
            agent_type: Agent type to validate
            
        Returns:
            bool: True if valid
            
        Raises:
            ValueError: If agent type is invalid
        """
        valid_types = ["sql_agent", "pandas_agent", "schema_analyzer"]
        
        if agent_type not in valid_types:
            raise ValueError(f"Invalid agent type. Must be one of: {', '.join(valid_types)}")
        
        return True
    
    @staticmethod
    def validate_pagination_params(page: int, page_size: int) -> bool:
        """
        Validate pagination parameters.
        
        Args:
            page: Page number
            page_size: Page size
            
        Returns:
            bool: True if valid
            
        Raises:
            ValueError: If parameters are invalid
        """
        if page < 1:
            raise ValueError("Page number must be >= 1")
        
        if page_size < 1 or page_size > 100:
            raise ValueError("Page size must be between 1 and 100")
        
        return True
    
    @staticmethod
    def sanitize_string(value: str, max_length: int = 1000) -> str:
        """
        Sanitize string input by removing dangerous characters.
        
        Args:
            value: String to sanitize
            max_length: Maximum allowed length
            
        Returns:
            str: Sanitized string
        """
        if not value:
            return ""
        
        # Remove null bytes and control characters
        sanitized = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', value)
        
        # Limit length
        if len(sanitized) > max_length:
            sanitized = sanitized[:max_length]
        
        return sanitized.strip()


class RequestValidator:
    """Request validation middleware and utilities."""
    
    @staticmethod
    def validate_content_type(content_type: str) -> bool:
        """Validate request content type."""
        allowed_types = [
            "application/json",
            "application/x-www-form-urlencoded",
            "multipart/form-data"
        ]
        
        if content_type:
            main_type = content_type.split(';')[0].strip().lower()
            return main_type in allowed_types
        
        return True  # Allow requests without content type
    
    @staticmethod
    def validate_request_size(content_length: Optional[int], max_size: int = 10 * 1024 * 1024) -> bool:
        """
        Validate request size.
        
        Args:
            content_length: Content length in bytes
            max_size: Maximum allowed size in bytes (default: 10MB)
            
        Returns:
            bool: True if valid
            
        Raises:
            ValueError: If request is too large
        """
        if content_length and content_length > max_size:
            raise ValueError(f"Request too large. Maximum size: {max_size} bytes")
        
        return True
    
    @staticmethod
    def validate_headers(headers: Dict[str, str]) -> bool:
        """
        Validate request headers for security.
        
        Args:
            headers: Request headers
            
        Returns:
            bool: True if valid
            
        Raises:
            ValueError: If headers contain dangerous content
        """
        dangerous_headers = ["x-forwarded-host", "x-original-url", "x-rewrite-url"]
        
        for header_name, header_value in headers.items():
            header_name_lower = header_name.lower()
            
            # Check for dangerous headers
            if header_name_lower in dangerous_headers:
                logger.warning(f"Potentially dangerous header detected: {header_name}")
            
            # Validate header values
            if len(header_value) > 8192:  # 8KB limit for header values
                raise ValueError(f"Header value too long: {header_name}")
        
        return True


def create_validation_error_response(error: ValidationError) -> Dict[str, Any]:
    """
    Create standardized validation error response.
    
    Args:
        error: Pydantic validation error
        
    Returns:
        dict: Formatted error response
    """
    validation_errors = []
    
    for err in error.errors():
        validation_errors.append({
            "field": ".".join(str(loc) for loc in err["loc"]),
            "message": err["msg"],
            "type": err["type"],
            "value": err.get("input")
        })
    
    return {
        "error": "Validation Error",
        "message": "Request validation failed",
        "validation_errors": validation_errors,
        "timestamp": datetime.now().isoformat()
    }


def handle_validation_exception(exc: ValidationError) -> HTTPException:
    """
    Convert Pydantic validation error to HTTP exception.
    
    Args:
        exc: Pydantic validation error
        
    Returns:
        HTTPException: Formatted HTTP exception
    """
    error_response = create_validation_error_response(exc)
    
    return HTTPException(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        detail=error_response
    )


# Custom validators for Pydantic models
def validate_sql_query_field(cls, v):
    """Pydantic validator for SQL query fields."""
    if v:
        ValidationUtils.validate_sql_query(v, allow_write_operations=False)
    return v


def validate_natural_query_field(cls, v):
    """Pydantic validator for natural language query fields."""
    if v:
        ValidationUtils.validate_natural_language_query(v)
    return ValidationUtils.sanitize_string(v, max_length=10000)


def validate_workflow_id_field(cls, v):
    """Pydantic validator for workflow ID fields."""
    if v:
        ValidationUtils.validate_workflow_id(v)
    return v


def validate_agent_type_field(cls, v):
    """Pydantic validator for agent type fields."""
    if v:
        ValidationUtils.validate_agent_type(v)
    return v


# Export validation utilities
__all__ = [
    "ValidationUtils",
    "RequestValidator", 
    "create_validation_error_response",
    "handle_validation_exception",
    "validate_sql_query_field",
    "validate_natural_query_field",
    "validate_workflow_id_field",
    "validate_agent_type_field"
]
