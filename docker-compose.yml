version: '3.8'

services:
  # FastAPI Backend
  api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///financial_data.db
      - GROQ_API_KEY=${GROQ_API_KEY}
      - DEBUG=false
      - LOG_LEVEL=INFO
      - RATE_LIMIT_PER_MINUTE=60
    volumes:
      - ./financial_data.db:/app/financial_data.db
      - ./logs:/app/logs
      - ./.env:/app/.env
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - analyst_network

  # Optional: PostgreSQL Database (uncomment if using PostgreSQL)
  # postgres:
  #   image: postgres:15-alpine
  #   environment:
  #     POSTGRES_DB: financial_analyst
  #     POSTGRES_USER: analyst
  #     POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #     - ./init.sql:/docker-entrypoint-initdb.d/init.sql
  #   ports:
  #     - "5432:5432"
  #   restart: unless-stopped
  #   networks:
  #     - analyst_network

  # Optional: Redis for caching (uncomment if using Redis)
  # redis:
  #   image: redis:7-alpine
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   restart: unless-stopped
  #   networks:
  #     - analyst_network

  # Optional: Nginx reverse proxy
  # nginx:
  #   image: nginx:alpine
  #   ports:
  #     - "80:80"
  #     - "443:443"
  #   volumes:
  #     - ./nginx.conf:/etc/nginx/nginx.conf
  #     - ./ssl:/etc/nginx/ssl
  #   depends_on:
  #     - api
  #   restart: unless-stopped
  #   networks:
  #     - analyst_network

networks:
  analyst_network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
