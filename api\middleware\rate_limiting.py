"""
Rate limiting middleware for FastAPI application.
Implements request throttling to prevent abuse and ensure fair usage.
"""

import logging
import time
from typing import Callable, Dict, Optional
from fastapi import Request, Response, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from slowapi.middleware import SlowAPIMiddleware
from config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


# Create limiter instance
limiter = Limiter(
    key_func=get_remote_address,
    default_limits=[f"{settings.security.rate_limit_per_minute}/minute"]
)


class RateLimitingMiddleware(BaseHTTPMiddleware):
    """Custom rate limiting middleware with enhanced features."""
    
    def __init__(self, app):
        super().__init__(app)
        self.request_counts: Dict[str, Dict[str, int]] = {}
        self.window_size = 60  # 1 minute window
        self.max_requests = settings.security.rate_limit_per_minute
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Apply rate limiting to requests."""
        client_ip = self._get_client_ip(request)
        current_time = int(time.time())
        window_start = current_time - (current_time % self.window_size)
        
        # Clean old entries
        self._cleanup_old_entries(current_time)
        
        # Check rate limit
        if self._is_rate_limited(client_ip, window_start):
            logger.warning(
                f"Rate limit exceeded for IP: {client_ip}",
                extra={
                    "client_ip": client_ip,
                    "endpoint": request.url.path,
                    "method": request.method
                }
            )
            
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail={
                    "error": "Rate limit exceeded",
                    "message": f"Too many requests. Limit: {self.max_requests} per minute",
                    "retry_after": self.window_size - (current_time % self.window_size)
                }
            )
        
        # Record request
        self._record_request(client_ip, window_start)
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers
        remaining = self._get_remaining_requests(client_ip, window_start)
        response.headers["X-RateLimit-Limit"] = str(self.max_requests)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(window_start + self.window_size)
        
        return response
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address from request."""
        # Check for forwarded headers first
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fall back to direct client IP
        return request.client.host if request.client else "unknown"
    
    def _is_rate_limited(self, client_ip: str, window_start: int) -> bool:
        """Check if client has exceeded rate limit."""
        if client_ip not in self.request_counts:
            return False
        
        client_data = self.request_counts[client_ip]
        current_count = client_data.get(str(window_start), 0)
        
        return current_count >= self.max_requests
    
    def _record_request(self, client_ip: str, window_start: int):
        """Record a request for rate limiting."""
        if client_ip not in self.request_counts:
            self.request_counts[client_ip] = {}
        
        window_key = str(window_start)
        self.request_counts[client_ip][window_key] = (
            self.request_counts[client_ip].get(window_key, 0) + 1
        )
    
    def _get_remaining_requests(self, client_ip: str, window_start: int) -> int:
        """Get remaining requests for client in current window."""
        if client_ip not in self.request_counts:
            return self.max_requests
        
        current_count = self.request_counts[client_ip].get(str(window_start), 0)
        return max(0, self.max_requests - current_count)
    
    def _cleanup_old_entries(self, current_time: int):
        """Remove old rate limiting entries to prevent memory leaks."""
        cutoff_time = current_time - (2 * self.window_size)  # Keep 2 windows of data
        
        for client_ip in list(self.request_counts.keys()):
            client_data = self.request_counts[client_ip]
            
            # Remove old windows
            for window_key in list(client_data.keys()):
                if int(window_key) < cutoff_time:
                    del client_data[window_key]
            
            # Remove client if no data left
            if not client_data:
                del self.request_counts[client_ip]


class EnhancedRateLimitingMiddleware(SlowAPIMiddleware):
    """Enhanced rate limiting middleware using slowapi."""
    
    def __init__(self, app):
        super().__init__(app)
        self.limiter = limiter


# Rate limit exception handler
async def rate_limit_handler(request: Request, exc: RateLimitExceeded):
    """Handle rate limit exceeded exceptions."""
    logger.warning(
        f"Rate limit exceeded: {exc.detail}",
        extra={
            "client_ip": get_remote_address(request),
            "endpoint": request.url.path,
            "method": request.method
        }
    )
    
    return Response(
        content={
            "error": "Rate limit exceeded",
            "detail": exc.detail,
            "retry_after": getattr(exc, 'retry_after', 60)
        },
        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
        headers={
            "Retry-After": str(getattr(exc, 'retry_after', 60))
        }
    )


# Export limiter for use in route decorators
__all__ = ["RateLimitingMiddleware", "EnhancedRateLimitingMiddleware", "limiter", "rate_limit_handler"]
