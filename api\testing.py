"""
API testing utilities and validation endpoints.
Provides tools for testing API functionality and validating system components.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel

from api.models.common_models import SuccessResponse, ErrorResponse
from api.services.workflow_service import WorkflowService
from api.services.agent_service import AgentService
from api.dependencies import (
    get_workflow_service, get_agent_service,
    get_database_config_dependency, get_agent_coordinator_dependency
)
from database.database_config import DatabaseConfig
from agents.agent_coordinator import CoordinadorAgentes

logger = logging.getLogger(__name__)

# Create router for testing endpoints
test_router = APIRouter(prefix="/test", tags=["Testing"])


class TestRequest(BaseModel):
    """Request model for API tests."""
    test_name: str
    parameters: Optional[Dict[str, Any]] = {}


class TestResult(BaseModel):
    """Result model for API tests."""
    test_name: str
    status: str
    duration: float
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime


class APITester:
    """Utility class for testing API functionality."""
    
    def __init__(self):
        self.test_results: List[TestResult] = []
    
    async def run_database_test(self, db_config: DatabaseConfig) -> TestResult:
        """Test database connectivity and basic operations."""
        start_time = datetime.now()
        
        try:
            # Test connection
            connection_ok = db_config.test_connection()
            
            if not connection_ok:
                return TestResult(
                    test_name="database_connectivity",
                    status="FAILED",
                    duration=0.0,
                    message="Database connection failed",
                    timestamp=start_time
                )
            
            # Test table listing
            tables = db_config.get_table_names()
            
            # Test basic query
            test_query = "SELECT 1 as test_value"
            result = db_config.execute_query(test_query)
            
            duration = (datetime.now() - start_time).total_seconds()
            
            return TestResult(
                test_name="database_connectivity",
                status="PASSED",
                duration=duration,
                message="Database tests passed",
                details={
                    "connection": "OK",
                    "tables_found": len(tables),
                    "query_test": "OK",
                    "query_result": result
                },
                timestamp=start_time
            )
            
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            return TestResult(
                test_name="database_connectivity",
                status="FAILED",
                duration=duration,
                message=f"Database test failed: {str(e)}",
                timestamp=start_time
            )
    
    async def run_agent_test(self, agent_service: AgentService) -> TestResult:
        """Test agent functionality."""
        start_time = datetime.now()
        
        try:
            # Test agent status
            status_response = await agent_service.get_agent_status()
            
            if status_response.active_agents == 0:
                return TestResult(
                    test_name="agent_functionality",
                    status="FAILED",
                    duration=0.0,
                    message="No active agents found",
                    timestamp=start_time
                )
            
            duration = (datetime.now() - start_time).total_seconds()
            
            return TestResult(
                test_name="agent_functionality",
                status="PASSED",
                duration=duration,
                message="Agent tests passed",
                details={
                    "total_agents": status_response.total_agents,
                    "active_agents": status_response.active_agents,
                    "agents": status_response.agents
                },
                timestamp=start_time
            )
            
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            return TestResult(
                test_name="agent_functionality",
                status="FAILED",
                duration=duration,
                message=f"Agent test failed: {str(e)}",
                timestamp=start_time
            )
    
    async def run_workflow_test(self, workflow_service: WorkflowService) -> TestResult:
        """Test workflow functionality."""
        start_time = datetime.now()
        
        try:
            from api.models.workflow_models import WorkflowCreateRequest
            
            # Create a test workflow
            test_request = WorkflowCreateRequest(
                query="Test workflow - get system information",
                workflow_type="test",
                priority=5,
                timeout=60,
                metadata={"test": True}
            )
            
            workflow = await workflow_service.create_workflow(test_request)
            
            if not workflow.workflow_id:
                return TestResult(
                    test_name="workflow_functionality",
                    status="FAILED",
                    duration=0.0,
                    message="Failed to create test workflow",
                    timestamp=start_time
                )
            
            # Test workflow status
            status = await workflow_service.get_workflow_status(workflow.workflow_id)
            
            # Cancel the test workflow
            await workflow_service.cancel_workflow(workflow.workflow_id, "Test completed")
            
            duration = (datetime.now() - start_time).total_seconds()
            
            return TestResult(
                test_name="workflow_functionality",
                status="PASSED",
                duration=duration,
                message="Workflow tests passed",
                details={
                    "workflow_id": workflow.workflow_id,
                    "status": status.status.value,
                    "tasks_total": status.total_tasks
                },
                timestamp=start_time
            )
            
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            return TestResult(
                test_name="workflow_functionality",
                status="FAILED",
                duration=duration,
                message=f"Workflow test failed: {str(e)}",
                timestamp=start_time
            )
    
    async def run_comprehensive_test(
        self,
        db_config: DatabaseConfig,
        agent_service: AgentService,
        workflow_service: WorkflowService
    ) -> List[TestResult]:
        """Run comprehensive API tests."""
        logger.info("Starting comprehensive API tests")
        
        tests = [
            self.run_database_test(db_config),
            self.run_agent_test(agent_service),
            self.run_workflow_test(workflow_service)
        ]
        
        results = await asyncio.gather(*tests, return_exceptions=True)
        
        # Handle any exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(TestResult(
                    test_name=f"test_{i}",
                    status="ERROR",
                    duration=0.0,
                    message=f"Test execution error: {str(result)}",
                    timestamp=datetime.now()
                ))
            else:
                processed_results.append(result)
        
        self.test_results.extend(processed_results)
        logger.info(f"Completed {len(processed_results)} API tests")
        
        return processed_results


# Global tester instance
_api_tester: Optional[APITester] = None


def get_api_tester() -> APITester:
    """Get the global API tester instance."""
    global _api_tester
    if _api_tester is None:
        _api_tester = APITester()
    return _api_tester


# Test endpoints
@test_router.post("/run", response_model=SuccessResponse)
async def run_api_tests(
    test_request: Optional[TestRequest] = None,
    db_config: DatabaseConfig = Depends(get_database_config_dependency),
    agent_service: AgentService = Depends(get_agent_service),
    workflow_service: WorkflowService = Depends(get_workflow_service)
):
    """
    Run API functionality tests.
    
    Executes comprehensive tests of all API components including:
    - Database connectivity
    - Agent functionality  
    - Workflow management
    - System health
    """
    try:
        tester = get_api_tester()
        
        if test_request and test_request.test_name:
            # Run specific test
            if test_request.test_name == "database":
                results = [await tester.run_database_test(db_config)]
            elif test_request.test_name == "agents":
                results = [await tester.run_agent_test(agent_service)]
            elif test_request.test_name == "workflows":
                results = [await tester.run_workflow_test(workflow_service)]
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Unknown test: {test_request.test_name}"
                )
        else:
            # Run comprehensive tests
            results = await tester.run_comprehensive_test(db_config, agent_service, workflow_service)
        
        # Calculate summary
        passed = sum(1 for r in results if r.status == "PASSED")
        failed = sum(1 for r in results if r.status == "FAILED")
        errors = sum(1 for r in results if r.status == "ERROR")
        
        return SuccessResponse(
            message=f"API tests completed: {passed} passed, {failed} failed, {errors} errors",
            data={
                "summary": {
                    "total_tests": len(results),
                    "passed": passed,
                    "failed": failed,
                    "errors": errors,
                    "success_rate": (passed / len(results)) * 100 if results else 0
                },
                "results": [
                    {
                        "test_name": r.test_name,
                        "status": r.status,
                        "duration": r.duration,
                        "message": r.message,
                        "details": r.details,
                        "timestamp": r.timestamp.isoformat()
                    }
                    for r in results
                ]
            }
        )
        
    except Exception as e:
        logger.error(f"API test execution failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to execute API tests"
        )


@test_router.get("/results", response_model=SuccessResponse)
async def get_test_results():
    """Get historical test results."""
    try:
        tester = get_api_tester()
        
        return SuccessResponse(
            message="Test results retrieved successfully",
            data={
                "total_results": len(tester.test_results),
                "results": [
                    {
                        "test_name": r.test_name,
                        "status": r.status,
                        "duration": r.duration,
                        "message": r.message,
                        "timestamp": r.timestamp.isoformat()
                    }
                    for r in tester.test_results[-50:]  # Last 50 results
                ]
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to get test results: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve test results"
        )


# Export testing utilities
__all__ = [
    "APITester",
    "TestResult",
    "TestRequest",
    "get_api_tester",
    "test_router"
]
