"""
Pydantic models for workflow management and orchestration.
"""

from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum

from .common_models import BaseResponse, ResponseStatus


class WorkflowStatus(str, Enum):
    """Workflow execution status enumeration."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskStatus(str, Enum):
    """Individual task status enumeration."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AgentType(str, Enum):
    """Available agent types for workflow tasks."""
    SQL_AGENT = "sql_agent"
    PANDAS_AGENT = "pandas_agent"
    SCHEMA_ANALYZER = "schema_analyzer"
    QUERY_PROCESSOR = "query_processor"
    FINANCIAL_ADVISOR = "financial_advisor"
    DASHBOARD_GENERATOR = "dashboard_generator"


class WorkflowCreateRequest(BaseModel):
    """Request model for creating a new workflow."""
    query: str = Field(..., min_length=1, max_length=10000, description="Natural language query or request")
    workflow_type: Optional[str] = Field("auto", description="Workflow type (auto, custom, analysis)")
    priority: Optional[int] = Field(1, ge=1, le=5, description="Workflow priority (1=highest, 5=lowest)")
    timeout: Optional[int] = Field(300, ge=30, le=3600, description="Workflow timeout in seconds")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional workflow metadata")
    
    @validator('query')
    def validate_query(cls, v):
        """Validate query content."""
        if not v.strip():
            raise ValueError('Query cannot be empty or whitespace only')
        return v.strip()
    
    class Config:
        json_schema_extra = {
            "example": {
                "query": "Analyze the sales data for the last quarter and create visualizations",
                "workflow_type": "analysis",
                "priority": 2,
                "timeout": 600,
                "metadata": {
                    "user_id": "user123",
                    "department": "finance"
                }
            }
        }


class TaskRequest(BaseModel):
    """Individual task configuration within a workflow."""
    task_id: str = Field(..., description="Unique task identifier")
    agent_type: AgentType = Field(..., description="Agent type to execute the task")
    task_data: Dict[str, Any] = Field(default_factory=dict, description="Task-specific data and parameters")
    dependencies: List[str] = Field(default_factory=list, description="List of task IDs this task depends on")
    timeout: Optional[int] = Field(120, ge=10, le=1800, description="Task timeout in seconds")
    retry_count: Optional[int] = Field(0, ge=0, le=3, description="Number of retry attempts")


class CustomWorkflowRequest(BaseModel):
    """Request model for creating custom workflows with specific task configuration."""
    workflow_name: str = Field(..., min_length=1, max_length=100, description="Workflow name")
    description: Optional[str] = Field(None, max_length=500, description="Workflow description")
    tasks: List[TaskRequest] = Field(..., min_items=1, description="List of tasks to execute")
    priority: Optional[int] = Field(1, ge=1, le=5, description="Workflow priority")
    timeout: Optional[int] = Field(600, ge=60, le=3600, description="Overall workflow timeout")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Workflow metadata")


class TaskResponse(BaseModel):
    """Response model for individual task information."""
    task_id: str = Field(..., description="Task identifier")
    agent_type: AgentType = Field(..., description="Agent type executing the task")
    status: TaskStatus = Field(..., description="Current task status")
    started_at: Optional[datetime] = Field(None, description="Task start time")
    completed_at: Optional[datetime] = Field(None, description="Task completion time")
    duration: Optional[float] = Field(None, description="Task execution duration in seconds")
    result: Optional[Dict[str, Any]] = Field(None, description="Task execution result")
    error: Optional[str] = Field(None, description="Error message if task failed")
    retry_count: int = Field(0, description="Number of retry attempts made")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class WorkflowResponse(BaseResponse):
    """Response model for workflow information."""
    workflow_id: str = Field(..., description="Unique workflow identifier")
    status: WorkflowStatus = Field(..., description="Current workflow status")
    query: Optional[str] = Field(None, description="Original query or request")
    created_at: datetime = Field(..., description="Workflow creation time")
    started_at: Optional[datetime] = Field(None, description="Workflow start time")
    completed_at: Optional[datetime] = Field(None, description="Workflow completion time")
    duration: Optional[float] = Field(None, description="Total workflow duration in seconds")
    priority: int = Field(1, description="Workflow priority")
    tasks: List[TaskResponse] = Field(default_factory=list, description="List of workflow tasks")
    results: Optional[Dict[str, Any]] = Field(None, description="Workflow execution results")
    error: Optional[str] = Field(None, description="Error message if workflow failed")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Workflow metadata")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class WorkflowStatusResponse(BaseResponse):
    """Simplified workflow status response."""
    workflow_id: str = Field(..., description="Workflow identifier")
    status: WorkflowStatus = Field(..., description="Current workflow status")
    progress: float = Field(0.0, ge=0.0, le=1.0, description="Workflow completion progress (0.0-1.0)")
    tasks_completed: int = Field(0, description="Number of completed tasks")
    total_tasks: int = Field(0, description="Total number of tasks")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion time")
    current_task: Optional[str] = Field(None, description="Currently executing task ID")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class WorkflowListResponse(BaseResponse):
    """Response model for listing workflows."""
    workflows: List[WorkflowResponse] = Field(default_factory=list, description="List of workflows")
    total_count: int = Field(0, description="Total number of workflows")
    active_count: int = Field(0, description="Number of active workflows")
    completed_count: int = Field(0, description="Number of completed workflows")
    failed_count: int = Field(0, description="Number of failed workflows")


class WorkflowCancelRequest(BaseModel):
    """Request model for canceling a workflow."""
    reason: Optional[str] = Field(None, max_length=200, description="Reason for cancellation")
    force: bool = Field(False, description="Force cancellation even if tasks are running")


class WorkflowExecutionRequest(BaseModel):
    """Request model for executing a workflow."""
    workflow_id: str = Field(..., description="Workflow identifier to execute")
    parameters: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Execution parameters")
    async_execution: bool = Field(True, description="Whether to execute asynchronously")


# Example models for documentation
class WorkflowExamples:
    """Example workflow models for API documentation."""
    
    CREATE_REQUEST = WorkflowCreateRequest(
        query="Show me the top 10 customers by revenue and create a chart",
        workflow_type="analysis",
        priority=2,
        timeout=300,
        metadata={"user_id": "user123"}
    )
    
    WORKFLOW_RESPONSE = WorkflowResponse(
        message="Workflow created successfully",
        workflow_id="wf_123456789",
        status=WorkflowStatus.IN_PROGRESS,
        query="Show me the top 10 customers by revenue",
        created_at=datetime.now(),
        started_at=datetime.now(),
        priority=2,
        tasks=[
            TaskResponse(
                task_id="task_1",
                agent_type=AgentType.SQL_AGENT,
                status=TaskStatus.COMPLETED,
                started_at=datetime.now(),
                completed_at=datetime.now(),
                duration=2.5
            )
        ]
    )
    
    STATUS_RESPONSE = WorkflowStatusResponse(
        message="Workflow status retrieved",
        workflow_id="wf_123456789",
        status=WorkflowStatus.IN_PROGRESS,
        progress=0.6,
        tasks_completed=3,
        total_tasks=5,
        current_task="task_4"
    )
