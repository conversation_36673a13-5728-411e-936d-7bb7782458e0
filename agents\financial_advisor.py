"""
Financial Advisor Agent for generating comprehensive financial reports and insights.
This agent synthesizes results from other agents to provide financial analysis and recommendations.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
import json

logger = logging.getLogger(__name__)


class AsesorFinanciero:
    """
    Financial Advisor Agent that creates comprehensive financial reports and recommendations.
    """
    
    def __init__(self):
        """Initialize the financial advisor."""
        self.report_templates = {
            'comprehensive': self._generate_comprehensive_report,
            'summary': self._generate_summary_report,
            'analysis': self._generate_analysis_report,
            'recommendations': self._generate_recommendations_report
        }
        
        logger.info("Financial advisor initialized successfully")
    
    def generar_reporte_financiero(self, consulta: str, resultados_previos: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Generate a comprehensive financial report based on query and previous results.
        
        Args:
            consulta (str): Original user query
            resultados_previos (Dict[str, Any]): Results from previous agents
            
        Returns:
            Dict[str, Any]: Comprehensive financial report
        """
        try:
            logger.info(f"Generating financial report for query: {consulta[:100]}...")
            
            # Analyze the query to determine report type
            report_type = self._determine_report_type(consulta)
            
            # Extract key insights from previous results
            insights = self._extract_insights(resultados_previos or {})
            
            # Generate the appropriate report
            report_generator = self.report_templates.get(report_type, self._generate_comprehensive_report)
            report = report_generator(consulta, insights)
            
            # Add metadata
            report.update({
                "success": True,
                "report_type": report_type,
                "generated_at": datetime.now().isoformat(),
                "query_original": consulta,
                "data_sources": self._identify_data_sources(resultados_previos or {})
            })
            
            logger.info(f"Financial report generated successfully. Type: {report_type}")
            return report
            
        except Exception as e:
            logger.error(f"Error generating financial report: {e}")
            return {
                "success": False,
                "error": str(e),
                "query_original": consulta,
                "generated_at": datetime.now().isoformat()
            }
    
    def _determine_report_type(self, consulta: str) -> str:
        """Determine the type of financial report to generate."""
        consulta_lower = consulta.lower()
        
        if any(word in consulta_lower for word in ['summary', 'brief', 'overview']):
            return 'summary'
        elif any(word in consulta_lower for word in ['recommend', 'suggest', 'advice', 'strategy']):
            return 'recommendations'
        elif any(word in consulta_lower for word in ['analyze', 'analysis', 'deep', 'detailed']):
            return 'analysis'
        else:
            return 'comprehensive'
    
    def _extract_insights(self, resultados_previos: Dict[str, Any]) -> Dict[str, Any]:
        """Extract key insights from previous agent results."""
        insights = {
            'sql_data': None,
            'analysis_results': None,
            'schema_info': None,
            'query_analysis': None,
            'key_metrics': [],
            'trends': [],
            'anomalies': []
        }
        
        # Extract SQL data
        if 'result_sql_analysis' in resultados_previos:
            sql_result = resultados_previos['result_sql_analysis']
            if sql_result.get('success') and 'data' in sql_result:
                insights['sql_data'] = sql_result['data']
                insights['key_metrics'].extend(self._extract_metrics_from_data(sql_result['data']))
        
        # Extract pandas analysis
        if 'result_pandas_analysis' in resultados_previos:
            pandas_result = resultados_previos['result_pandas_analysis']
            if pandas_result.get('success'):
                insights['analysis_results'] = pandas_result
                if 'trends' in pandas_result:
                    insights['trends'].extend(pandas_result['trends'])
        
        # Extract schema information
        if 'result_schema_analysis' in resultados_previos:
            schema_result = resultados_previos['result_schema_analysis']
            if schema_result.get('success'):
                insights['schema_info'] = schema_result.get('schema_info', {})
        
        # Extract query analysis
        if 'result_query_analysis' in resultados_previos:
            query_result = resultados_previos['result_query_analysis']
            if query_result.get('success'):
                insights['query_analysis'] = query_result
        
        return insights
    
    def _extract_metrics_from_data(self, data: List[Dict]) -> List[Dict[str, Any]]:
        """Extract key financial metrics from data."""
        metrics = []
        
        if not data:
            return metrics
        
        # Analyze numeric columns for potential financial metrics
        numeric_columns = []
        if data:
            first_row = data[0]
            for key, value in first_row.items():
                if isinstance(value, (int, float)):
                    numeric_columns.append(key)
        
        # Calculate basic statistics for numeric columns
        for column in numeric_columns:
            values = [row.get(column, 0) for row in data if isinstance(row.get(column), (int, float))]
            if values:
                metrics.append({
                    'metric': column,
                    'total': sum(values),
                    'average': sum(values) / len(values),
                    'count': len(values),
                    'max': max(values),
                    'min': min(values)
                })
        
        return metrics
    
    def _generate_comprehensive_report(self, consulta: str, insights: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a comprehensive financial report."""
        report = {
            "title": "Comprehensive Financial Analysis Report",
            "executive_summary": self._generate_executive_summary(insights),
            "key_findings": self._generate_key_findings(insights),
            "financial_metrics": insights.get('key_metrics', []),
            "trend_analysis": self._generate_trend_analysis(insights),
            "recommendations": self._generate_recommendations(insights),
            "data_quality": self._assess_data_quality(insights),
            "methodology": "Analysis based on SQL queries, statistical analysis, and financial modeling"
        }
        
        return report
    
    def _generate_summary_report(self, consulta: str, insights: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a summary financial report."""
        return {
            "title": "Financial Summary Report",
            "executive_summary": self._generate_executive_summary(insights),
            "key_metrics": insights.get('key_metrics', [])[:5],  # Top 5 metrics
            "main_insights": self._generate_key_findings(insights)[:3]  # Top 3 findings
        }
    
    def _generate_analysis_report(self, consulta: str, insights: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a detailed analysis report."""
        return {
            "title": "Detailed Financial Analysis",
            "analysis_overview": self._generate_executive_summary(insights),
            "detailed_findings": self._generate_key_findings(insights),
            "statistical_analysis": insights.get('analysis_results', {}),
            "trend_analysis": self._generate_trend_analysis(insights),
            "risk_assessment": self._generate_risk_assessment(insights)
        }
    
    def _generate_recommendations_report(self, consulta: str, insights: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a recommendations-focused report."""
        return {
            "title": "Financial Recommendations Report",
            "situation_overview": self._generate_executive_summary(insights),
            "strategic_recommendations": self._generate_recommendations(insights),
            "action_items": self._generate_action_items(insights),
            "expected_outcomes": self._generate_expected_outcomes(insights)
        }
    
    def _generate_executive_summary(self, insights: Dict[str, Any]) -> str:
        """Generate executive summary based on insights."""
        summary_parts = []
        
        # Data overview
        if insights.get('sql_data'):
            data_count = len(insights['sql_data'])
            summary_parts.append(f"Analysis based on {data_count} data records.")
        
        # Key metrics summary
        if insights.get('key_metrics'):
            metrics_count = len(insights['key_metrics'])
            summary_parts.append(f"Evaluated {metrics_count} key financial metrics.")
        
        # Trends summary
        if insights.get('trends'):
            summary_parts.append("Identified significant trends in the data.")
        
        if not summary_parts:
            summary_parts.append("Comprehensive financial analysis completed based on available data.")
        
        return " ".join(summary_parts)
    
    def _generate_key_findings(self, insights: Dict[str, Any]) -> List[str]:
        """Generate key findings from insights."""
        findings = []
        
        # Analyze metrics for findings
        for metric in insights.get('key_metrics', []):
            if metric.get('total', 0) > 0:
                findings.append(f"{metric['metric']}: Total of {metric['total']:,.2f} with average of {metric['average']:,.2f}")
        
        # Add trend findings
        for trend in insights.get('trends', []):
            if isinstance(trend, dict) and 'description' in trend:
                findings.append(trend['description'])
            elif isinstance(trend, str):
                findings.append(trend)
        
        if not findings:
            findings.append("Analysis completed successfully with available data.")
        
        return findings[:10]  # Limit to top 10 findings
    
    def _generate_trend_analysis(self, insights: Dict[str, Any]) -> Dict[str, Any]:
        """Generate trend analysis."""
        return {
            "trends_identified": len(insights.get('trends', [])),
            "trend_summary": "Trend analysis based on available data patterns",
            "trends": insights.get('trends', [])
        }
    
    def _generate_recommendations(self, insights: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on insights."""
        recommendations = []
        
        # Generate recommendations based on metrics
        for metric in insights.get('key_metrics', []):
            if metric.get('average', 0) > metric.get('max', 0) * 0.8:
                recommendations.append(f"Consider optimizing {metric['metric']} performance")
        
        if not recommendations:
            recommendations.extend([
                "Continue monitoring key financial metrics",
                "Implement regular data analysis processes",
                "Consider expanding data collection for deeper insights"
            ])
        
        return recommendations[:5]  # Limit to top 5 recommendations
    
    def _generate_risk_assessment(self, insights: Dict[str, Any]) -> Dict[str, Any]:
        """Generate risk assessment."""
        return {
            "risk_level": "Medium",
            "risk_factors": ["Data quality", "Market volatility", "Operational efficiency"],
            "mitigation_strategies": ["Regular monitoring", "Diversification", "Process improvement"]
        }
    
    def _generate_action_items(self, insights: Dict[str, Any]) -> List[str]:
        """Generate actionable items."""
        return [
            "Review and validate data sources",
            "Implement recommended optimizations",
            "Schedule regular performance reviews",
            "Monitor key metrics continuously"
        ]
    
    def _generate_expected_outcomes(self, insights: Dict[str, Any]) -> List[str]:
        """Generate expected outcomes."""
        return [
            "Improved financial performance visibility",
            "Enhanced decision-making capabilities",
            "Better risk management",
            "Optimized operational efficiency"
        ]
    
    def _assess_data_quality(self, insights: Dict[str, Any]) -> Dict[str, Any]:
        """Assess the quality of available data."""
        quality_score = 0.8  # Default score
        
        if insights.get('sql_data'):
            quality_score += 0.1
        if insights.get('analysis_results'):
            quality_score += 0.1
        
        return {
            "quality_score": min(quality_score, 1.0),
            "completeness": "Good",
            "reliability": "High",
            "recommendations": ["Continue current data collection practices"]
        }
    
    def _identify_data_sources(self, resultados_previos: Dict[str, Any]) -> List[str]:
        """Identify data sources used in the analysis."""
        sources = []
        
        if 'result_sql_analysis' in resultados_previos:
            sources.append("Database queries")
        if 'result_pandas_analysis' in resultados_previos:
            sources.append("Statistical analysis")
        if 'result_schema_analysis' in resultados_previos:
            sources.append("Database schema")
        
        return sources if sources else ["Internal data analysis"]


# Export the agent class
__all__ = ['AsesorFinanciero']
