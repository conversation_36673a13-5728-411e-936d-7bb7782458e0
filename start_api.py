#!/usr/bin/env python3
"""
Startup script for the Database Analyst FastAPI backend.
Provides a convenient way to start the API server with proper configuration.
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    import uvicorn
    from api.main import app
    from config.settings import get_settings
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Please ensure all dependencies are installed:")
    print("pip install -r requirements.txt")
    sys.exit(1)


def setup_logging(log_level: str = "INFO"):
    """Configure logging for the application."""
    # Create logs directory if it doesn't exist
    logs_dir = project_root / "logs"
    logs_dir.mkdir(exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(logs_dir / "api.log")
        ]
    )
    
    # Set specific logger levels
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.INFO)
    logging.getLogger("fastapi").setLevel(logging.INFO)


def check_environment():
    """Check if the environment is properly configured."""
    issues = []
    
    # Check for required environment variables
    required_env_vars = ["GROQ_API_KEY"]
    for var in required_env_vars:
        if not os.getenv(var):
            issues.append(f"Missing required environment variable: {var}")
    
    # Check if database file exists (for SQLite)
    db_url = os.getenv("DATABASE_URL", "sqlite:///financial_data.db")
    if db_url.startswith("sqlite:///"):
        db_file = db_url.replace("sqlite:///", "")
        if not os.path.exists(db_file):
            issues.append(f"Database file not found: {db_file}")
    
    # Check if .env file exists
    env_file = project_root / ".env"
    if not env_file.exists():
        issues.append(".env file not found. Please create one based on .env.example")
    
    return issues


def main():
    """Main entry point for the API server."""
    parser = argparse.ArgumentParser(description="Database Analyst FastAPI Backend")
    parser.add_argument(
        "--host", 
        default="0.0.0.0", 
        help="Host to bind the server to (default: 0.0.0.0)"
    )
    parser.add_argument(
        "--port", 
        type=int, 
        default=8000, 
        help="Port to bind the server to (default: 8000)"
    )
    parser.add_argument(
        "--reload", 
        action="store_true", 
        help="Enable auto-reload for development"
    )
    parser.add_argument(
        "--log-level", 
        choices=["DEBUG", "INFO", "WARNING", "ERROR"], 
        default="INFO",
        help="Set the logging level (default: INFO)"
    )
    parser.add_argument(
        "--workers", 
        type=int, 
        default=1, 
        help="Number of worker processes (default: 1)"
    )
    parser.add_argument(
        "--check-env", 
        action="store_true", 
        help="Check environment configuration and exit"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    # Check environment
    env_issues = check_environment()
    if env_issues:
        logger.error("Environment configuration issues found:")
        for issue in env_issues:
            logger.error(f"  - {issue}")
        
        if args.check_env:
            sys.exit(1)
        else:
            logger.warning("Continuing despite environment issues...")
    
    if args.check_env:
        logger.info("Environment check passed!")
        sys.exit(0)
    
    # Load settings
    try:
        settings = get_settings()
        logger.info("Settings loaded successfully")
    except Exception as e:
        logger.error(f"Failed to load settings: {e}")
        sys.exit(1)
    
    # Print startup information
    logger.info("=" * 60)
    logger.info("🚀 Database Analyst FastAPI Backend")
    logger.info("=" * 60)
    logger.info(f"Host: {args.host}")
    logger.info(f"Port: {args.port}")
    logger.info(f"Reload: {args.reload}")
    logger.info(f"Log Level: {args.log_level}")
    logger.info(f"Workers: {args.workers}")
    logger.info(f"Debug Mode: {settings.app.debug}")
    logger.info("=" * 60)
    logger.info("📚 API Documentation:")
    logger.info(f"  Swagger UI: http://{args.host}:{args.port}/docs")
    logger.info(f"  ReDoc: http://{args.host}:{args.port}/redoc")
    logger.info("=" * 60)
    logger.info("🔗 Available Endpoints:")
    logger.info(f"  Health Check: http://{args.host}:{args.port}/health")
    logger.info(f"  System Status: http://{args.host}:{args.port}/system/info")
    logger.info(f"  Workflows: http://{args.host}:{args.port}/workflows")
    logger.info(f"  Agents: http://{args.host}:{args.port}/agents")
    logger.info("=" * 60)
    
    # Configure uvicorn
    uvicorn_config = {
        "app": "api.main:app",
        "host": args.host,
        "port": args.port,
        "log_level": args.log_level.lower(),
        "reload": args.reload,
        "workers": args.workers if not args.reload else 1,  # Reload doesn't work with multiple workers
        "access_log": True,
        "use_colors": True,
    }
    
    # Add SSL configuration if certificates are available
    ssl_keyfile = os.getenv("SSL_KEYFILE")
    ssl_certfile = os.getenv("SSL_CERTFILE")
    if ssl_keyfile and ssl_certfile:
        if os.path.exists(ssl_keyfile) and os.path.exists(ssl_certfile):
            uvicorn_config.update({
                "ssl_keyfile": ssl_keyfile,
                "ssl_certfile": ssl_certfile
            })
            logger.info("🔒 SSL/TLS enabled")
        else:
            logger.warning("SSL certificate files not found, running without SSL")
    
    try:
        # Start the server
        logger.info("🌟 Starting FastAPI server...")
        uvicorn.run(**uvicorn_config)
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Server failed to start: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
