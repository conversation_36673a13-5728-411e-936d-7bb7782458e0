"""
Simplified Database Analyst System - Core functionality without complex dependencies.
This version focuses on the essential features to get you started quickly.
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import numpy as np
import json
import os
import sys

# Add database path
sys.path.append(os.path.join(os.path.dirname(__file__), 'database'))

try:
    from database.database_config import get_database_config
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False
    st.warning("Database module not available. Using demo mode.")

# Configure Streamlit page
st.set_page_config(
    page_title="Database Analyst System",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .chat-message {
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
        border-left: 4px solid #1f77b4;
        background-color: #f8f9fa;
    }
    
    .user-message {
        background-color: #e3f2fd;
        border-left-color: #2196f3;
    }
    
    .assistant-message {
        background-color: #f1f8e9;
        border-left-color: #4caf50;
    }
</style>
""", unsafe_allow_html=True)

class SimpleDatabaseAnalyst:
    """Simplified Database Analyst System."""
    
    def __init__(self):
        """Initialize the application."""
        if DATABASE_AVAILABLE:
            self.db_config = get_database_config()
        else:
            self.db_config = None
        
        # Initialize session state
        if 'chat_history' not in st.session_state:
            st.session_state.chat_history = []
        if 'sample_data' not in st.session_state:
            st.session_state.sample_data = self.create_sample_data()
    
    def create_sample_data(self):
        """Create sample financial data."""
        dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')
        np.random.seed(42)  # For reproducible results
        
        # Generate realistic financial data
        base_price = 100
        returns = np.random.normal(0.001, 0.02, len(dates))
        prices = [base_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        return pd.DataFrame({
            'Date': dates,
            'Open': [p * (1 + np.random.normal(0, 0.005)) for p in prices],
            'High': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'Low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'Close': prices,
            'Volume': np.random.randint(1000000, 5000000, len(dates))
        })
    
    def run(self):
        """Run the main application."""
        # Header
        st.markdown('<h1 class="main-header">🤖 Database Analyst System</h1>', unsafe_allow_html=True)
        st.markdown("*Simplified version - Core functionality*")
        st.markdown("---")
        
        # Sidebar
        self.render_sidebar()
        
        # Main content
        col1, col2 = st.columns([1, 2])
        
        with col1:
            self.render_chat_interface()
        
        with col2:
            self.render_dashboard()
    
    def render_sidebar(self):
        """Render the sidebar."""
        with st.sidebar:
            st.header("🔧 System Status")
            
            # Database status
            if DATABASE_AVAILABLE and self.db_config:
                try:
                    if self.db_config.test_connection():
                        st.success("✅ Database Connected")
                    else:
                        st.warning("⚠️ Database Connection Issues")
                except:
                    st.error("❌ Database Error")
            else:
                st.info("📊 Demo Mode Active")
            
            # Quick actions
            st.header("🚀 Quick Actions")
            
            if st.button("📈 Generate Sample Chart"):
                self.create_sample_chart()
            
            if st.button("📊 Show Data Table"):
                st.session_state.show_data_table = True
            
            if st.button("🔄 Refresh Data"):
                st.session_state.sample_data = self.create_sample_data()
                st.success("Data refreshed!")
            
            if st.button("🗑️ Clear Chat"):
                st.session_state.chat_history = []
                st.rerun()
            
            # Settings
            st.header("⚙️ Settings")
            chart_type = st.selectbox("Chart Type", ["line", "candlestick", "bar", "area"])
            st.session_state.chart_type = chart_type
            
            time_period = st.selectbox("Time Period", ["1M", "3M", "6M", "1Y"])
            st.session_state.time_period = time_period
    
    def render_chat_interface(self):
        """Render the chat interface."""
        st.header("💬 Chat Interface")
        
        # Display chat history
        for message in st.session_state.chat_history:
            self.render_chat_message(message)
        
        # Chat input
        with st.form("chat_form", clear_on_submit=True):
            user_input = st.text_area(
                "Ask me about your data:",
                placeholder="Try: 'Show me a price chart' or 'Analyze the data trends'",
                height=100
            )
            
            if st.form_submit_button("Send 🚀"):
                if user_input:
                    self.process_user_message(user_input)
    
    def render_chat_message(self, message):
        """Render individual chat message."""
        message_type = message.get("type", "user")
        content = message.get("content", "")
        timestamp = message.get("timestamp", datetime.now())
        
        if message_type == "user":
            st.markdown(f"""
            <div class="chat-message user-message">
                <strong>You ({timestamp.strftime('%H:%M')})</strong><br>
                {content}
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown(f"""
            <div class="chat-message assistant-message">
                <strong>Assistant ({timestamp.strftime('%H:%M')})</strong><br>
                {content}
            </div>
            """, unsafe_allow_html=True)
    
    def render_dashboard(self):
        """Render the dashboard area."""
        st.header("📊 Interactive Dashboard")
        
        # Show sample chart
        if hasattr(st.session_state, 'current_chart'):
            st.plotly_chart(st.session_state.current_chart, use_container_width=True)
        else:
            # Default chart
            self.create_sample_chart()
        
        # Show data table if requested
        if getattr(st.session_state, 'show_data_table', False):
            st.subheader("📋 Data Table")
            st.dataframe(st.session_state.sample_data, use_container_width=True)
            
            # Data statistics
            st.subheader("📈 Data Statistics")
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("Total Records", len(st.session_state.sample_data))
            with col2:
                st.metric("Avg Close Price", f"${st.session_state.sample_data['Close'].mean():.2f}")
            with col3:
                st.metric("Max Price", f"${st.session_state.sample_data['High'].max():.2f}")
            with col4:
                st.metric("Min Price", f"${st.session_state.sample_data['Low'].min():.2f}")
    
    def process_user_message(self, user_input):
        """Process user message and generate response."""
        # Add user message
        user_message = {
            "type": "user",
            "content": user_input,
            "timestamp": datetime.now()
        }
        st.session_state.chat_history.append(user_message)
        
        # Generate response based on input
        response = self.generate_response(user_input)
        
        # Add assistant response
        assistant_message = {
            "type": "assistant",
            "content": response,
            "timestamp": datetime.now()
        }
        st.session_state.chat_history.append(assistant_message)
        
        st.rerun()
    
    def generate_response(self, user_input):
        """Generate response based on user input."""
        user_input_lower = user_input.lower()
        
        if any(word in user_input_lower for word in ['chart', 'graph', 'plot', 'visualize']):
            self.create_sample_chart()
            return "I've created a chart for you! You can see it in the dashboard area."
        
        elif any(word in user_input_lower for word in ['data', 'table', 'show']):
            st.session_state.show_data_table = True
            return "Here's the data table with statistics. You can see it in the dashboard area."
        
        elif any(word in user_input_lower for word in ['analyze', 'analysis', 'trend']):
            return self.analyze_data()
        
        elif any(word in user_input_lower for word in ['help', 'what can you do']):
            return """I can help you with:
            
            📊 **Visualizations**: "Show me a chart" or "Create a price graph"
            📋 **Data Analysis**: "Analyze the trends" or "Show me statistics"  
            📈 **Charts**: Line charts, candlestick charts, bar charts
            🔍 **Data Exploration**: "Show me the data table"
            
            Try asking me to create different types of charts or analyze the data!"""
        
        else:
            return f"I understand you're asking about: '{user_input}'. I can help you visualize and analyze data. Try asking me to 'show a chart' or 'analyze the data trends'."
    
    def analyze_data(self):
        """Analyze the sample data."""
        data = st.session_state.sample_data
        
        # Calculate some basic statistics
        price_change = ((data['Close'].iloc[-1] - data['Close'].iloc[0]) / data['Close'].iloc[0]) * 100
        volatility = data['Close'].pct_change().std() * 100
        avg_volume = data['Volume'].mean()
        
        return f"""📈 **Data Analysis Results:**
        
        **Price Performance:**
        - Total return: {price_change:.2f}%
        - Current price: ${data['Close'].iloc[-1]:.2f}
        - Price range: ${data['Low'].min():.2f} - ${data['High'].max():.2f}
        
        **Risk Metrics:**
        - Daily volatility: {volatility:.2f}%
        - Average volume: {avg_volume:,.0f}
        
        **Trend:** {'Upward' if price_change > 0 else 'Downward'} trend over the period.
        """
    
    def create_sample_chart(self):
        """Create a sample chart."""
        data = st.session_state.sample_data
        chart_type = getattr(st.session_state, 'chart_type', 'line')
        
        if chart_type == "candlestick":
            fig = go.Figure(data=go.Candlestick(
                x=data['Date'],
                open=data['Open'],
                high=data['High'],
                low=data['Low'],
                close=data['Close']
            ))
            fig.update_layout(title="Candlestick Chart", xaxis_rangeslider_visible=False)
        
        elif chart_type == "bar":
            fig = px.bar(data.tail(30), x='Date', y='Volume', title="Volume Chart (Last 30 Days)")
        
        elif chart_type == "area":
            fig = px.area(data, x='Date', y='Close', title="Price Area Chart")
        
        else:  # line chart
            fig = go.Figure()
            fig.add_trace(go.Scatter(x=data['Date'], y=data['Close'], mode='lines', name='Close Price'))
            fig.update_layout(title="Price Line Chart", xaxis_title="Date", yaxis_title="Price ($)")
        
        fig.update_layout(template="plotly_white")
        st.session_state.current_chart = fig

# Run the application
if __name__ == "__main__":
    app = SimpleDatabaseAnalyst()
    app.run()
