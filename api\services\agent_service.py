"""
Agent service layer for direct agent communication through the API.
Provides interface to individual agents without workflow orchestration.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import pandas as pd

from agents.agent_coordinator import get_agent_coordinator
from agents.sql_agent import AgenteSQLConsultas
from agents.enhanced_pandas_agent import AgenteAnalizarDatosAvanzado
from agents.schema_analyzer import AnalizadorEsquema

from api.models.agent_models import (
    SQLQueryRequest, SQLQueryResponse, DirectSQLRequest,
    PandasAnalysisRequest, PandasAnalysisResponse,
    SchemaAnalysisRequest, SchemaAnalysisResponse,
    AgentStatusResponse, BatchRequest, BatchResponse
)
from api.models.common_models import ResponseStatus, HealthStatus

logger = logging.getLogger(__name__)


class AgentService:
    """Service for direct agent communication and management."""
    
    def __init__(self):
        """Initialize agent service."""
        self.coordinator = get_agent_coordinator()
        self._agent_instances = {}
        self._initialize_agents()
    
    def _initialize_agents(self):
        """Initialize individual agent instances."""
        try:
            self._agent_instances = {
                "sql_agent": AgenteSQLConsultas(),
                "pandas_agent": AgenteAnalizarDatosAvanzado(),
                "schema_analyzer": AnalizadorEsquema()
            }
            logger.info("Agent instances initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize agent instances: {e}")
            raise
    
    async def execute_sql_query(self, request: SQLQueryRequest) -> SQLQueryResponse:
        """Execute natural language SQL query."""
        try:
            logger.info(f"Executing SQL query: {request.query[:100]}...")
            
            start_time = datetime.now()
            sql_agent = self._agent_instances["sql_agent"]
            
            # Execute query using SQL agent
            result = sql_agent.ejecutar_consulta_natural(request.query)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Process result
            if result.get("success", False):
                return SQLQueryResponse(
                    message="SQL query executed successfully",
                    agent_type="sql_agent",
                    execution_time=execution_time,
                    sql_query=result.get("sql_query"),
                    query_explanation=result.get("explanation"),
                    data=result.get("data", []),
                    row_count=len(result.get("data", [])),
                    columns=result.get("columns", []),
                    query_plan=result.get("query_plan"),
                    result=result
                )
            else:
                return SQLQueryResponse(
                    status=ResponseStatus.ERROR,
                    message=f"SQL query failed: {result.get('error', 'Unknown error')}",
                    agent_type="sql_agent",
                    execution_time=execution_time,
                    result=result
                )
                
        except Exception as e:
            logger.error(f"Failed to execute SQL query: {e}")
            return SQLQueryResponse(
                status=ResponseStatus.ERROR,
                message=f"SQL query execution failed: {str(e)}",
                agent_type="sql_agent",
                execution_time=0
            )
    
    async def execute_direct_sql(self, request: DirectSQLRequest) -> SQLQueryResponse:
        """Execute direct SQL query."""
        try:
            logger.info(f"Executing direct SQL: {request.sql_query[:100]}...")
            
            start_time = datetime.now()
            sql_agent = self._agent_instances["sql_agent"]
            
            # Execute direct SQL
            result = sql_agent.ejecutar_sql_directo(request.sql_query)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Process result
            if result.get("success", False):
                return SQLQueryResponse(
                    message="Direct SQL executed successfully",
                    agent_type="sql_agent",
                    execution_time=execution_time,
                    sql_query=request.sql_query,
                    data=result.get("data", []),
                    row_count=len(result.get("data", [])),
                    columns=result.get("columns", []),
                    result=result
                )
            else:
                return SQLQueryResponse(
                    status=ResponseStatus.ERROR,
                    message=f"Direct SQL failed: {result.get('error', 'Unknown error')}",
                    agent_type="sql_agent",
                    execution_time=execution_time,
                    result=result
                )
                
        except Exception as e:
            logger.error(f"Failed to execute direct SQL: {e}")
            return SQLQueryResponse(
                status=ResponseStatus.ERROR,
                message=f"Direct SQL execution failed: {str(e)}",
                agent_type="sql_agent",
                execution_time=0
            )
    
    async def execute_pandas_analysis(self, request: PandasAnalysisRequest) -> PandasAnalysisResponse:
        """Execute pandas data analysis."""
        try:
            logger.info(f"Executing pandas analysis: {request.query[:100]}...")
            
            start_time = datetime.now()
            pandas_agent = self._agent_instances["pandas_agent"]
            
            # Get data for analysis (this would typically come from a previous SQL query)
            # For now, we'll use mock data or get from database
            data = await self._get_analysis_data(request.data_source)
            
            if data is None or data.empty:
                return PandasAnalysisResponse(
                    status=ResponseStatus.ERROR,
                    message="No data available for analysis",
                    agent_type="pandas_agent",
                    execution_time=0
                )
            
            # Execute analysis
            result = pandas_agent.ejecutar_analisis_avanzado(
                data, 
                request.query, 
                request.analysis_type.value
            )
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Process result
            if result.get("success", False):
                return PandasAnalysisResponse(
                    message="Pandas analysis completed successfully",
                    agent_type="pandas_agent",
                    execution_time=execution_time,
                    analysis_summary=result.get("analisis_principal"),
                    statistical_results=result.get("estadisticas"),
                    visualizations=result.get("visualizaciones"),
                    insights=result.get("insights", []),
                    data_quality_issues=result.get("data_quality_issues", []),
                    recommendations=result.get("recommendations", []),
                    result=result
                )
            else:
                return PandasAnalysisResponse(
                    status=ResponseStatus.ERROR,
                    message=f"Pandas analysis failed: {result.get('error', 'Unknown error')}",
                    agent_type="pandas_agent",
                    execution_time=execution_time,
                    result=result
                )
                
        except Exception as e:
            logger.error(f"Failed to execute pandas analysis: {e}")
            return PandasAnalysisResponse(
                status=ResponseStatus.ERROR,
                message=f"Pandas analysis failed: {str(e)}",
                agent_type="pandas_agent",
                execution_time=0
            )
    
    async def execute_schema_analysis(self, request: SchemaAnalysisRequest) -> SchemaAnalysisResponse:
        """Execute database schema analysis."""
        try:
            logger.info(f"Executing schema analysis: {request.analysis_type}")
            
            start_time = datetime.now()
            schema_analyzer = self._agent_instances["schema_analyzer"]
            
            # Execute schema analysis
            if request.analysis_type == "comprehensive":
                result = schema_analyzer.generar_reporte_esquema()
            else:
                # Handle other analysis types
                result = {"success": True, "schema_info": "Basic schema analysis"}
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Process result
            if result.get("success", True):
                return SchemaAnalysisResponse(
                    message="Schema analysis completed successfully",
                    agent_type="schema_analyzer",
                    execution_time=execution_time,
                    schema_summary=result.get("schema_summary"),
                    table_analysis=result.get("table_analysis", []),
                    relationships=result.get("relationships", []),
                    optimization_suggestions=result.get("optimization_suggestions", []),
                    data_quality_report=result.get("data_quality_report"),
                    result=result
                )
            else:
                return SchemaAnalysisResponse(
                    status=ResponseStatus.ERROR,
                    message=f"Schema analysis failed: {result.get('error', 'Unknown error')}",
                    agent_type="schema_analyzer",
                    execution_time=execution_time,
                    result=result
                )
                
        except Exception as e:
            logger.error(f"Failed to execute schema analysis: {e}")
            return SchemaAnalysisResponse(
                status=ResponseStatus.ERROR,
                message=f"Schema analysis failed: {str(e)}",
                agent_type="schema_analyzer",
                execution_time=0
            )
    
    async def get_agent_status(self, agent_types: Optional[List[str]] = None) -> AgentStatusResponse:
        """Get status of agents."""
        try:
            agents_info = {}
            active_count = 0
            
            # Check specific agents or all agents
            agents_to_check = agent_types or list(self._agent_instances.keys())
            
            for agent_type in agents_to_check:
                if agent_type in self._agent_instances:
                    try:
                        # Basic health check - try to access the agent
                        agent = self._agent_instances[agent_type]
                        status = HealthStatus.HEALTHY
                        active_count += 1
                        
                        agents_info[agent_type] = {
                            "status": status.value,
                            "last_check": datetime.now().isoformat(),
                            "available": True
                        }
                    except Exception as e:
                        agents_info[agent_type] = {
                            "status": HealthStatus.UNHEALTHY.value,
                            "last_check": datetime.now().isoformat(),
                            "available": False,
                            "error": str(e)
                        }
                else:
                    agents_info[agent_type] = {
                        "status": HealthStatus.UNHEALTHY.value,
                        "last_check": datetime.now().isoformat(),
                        "available": False,
                        "error": "Agent not found"
                    }
            
            return AgentStatusResponse(
                message="Agent status retrieved successfully",
                agents=agents_info,
                total_agents=len(agents_to_check),
                active_agents=active_count
            )
            
        except Exception as e:
            logger.error(f"Failed to get agent status: {e}")
            return AgentStatusResponse(
                status=ResponseStatus.ERROR,
                message=f"Failed to get agent status: {str(e)}",
                agents={},
                total_agents=0,
                active_agents=0
            )
    
    async def execute_batch_requests(self, batch_request: BatchRequest) -> BatchResponse:
        """Execute multiple requests in batch."""
        try:
            logger.info(f"Executing batch of {len(batch_request.requests)} requests")
            
            start_time = datetime.now()
            results = []
            successful_count = 0
            failed_count = 0
            
            if batch_request.parallel_execution:
                # Execute in parallel
                tasks = []
                for request in batch_request.requests:
                    if isinstance(request, SQLQueryRequest):
                        tasks.append(self.execute_sql_query(request))
                    elif isinstance(request, PandasAnalysisRequest):
                        tasks.append(self.execute_pandas_analysis(request))
                    elif isinstance(request, SchemaAnalysisRequest):
                        tasks.append(self.execute_schema_analysis(request))
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
            else:
                # Execute sequentially
                for request in batch_request.requests:
                    try:
                        if isinstance(request, SQLQueryRequest):
                            result = await self.execute_sql_query(request)
                        elif isinstance(request, PandasAnalysisRequest):
                            result = await self.execute_pandas_analysis(request)
                        elif isinstance(request, SchemaAnalysisRequest):
                            result = await self.execute_schema_analysis(request)
                        else:
                            continue
                        
                        results.append(result)
                        
                        if result.status == ResponseStatus.SUCCESS:
                            successful_count += 1
                        else:
                            failed_count += 1
                            if batch_request.stop_on_error:
                                break
                                
                    except Exception as e:
                        failed_count += 1
                        if batch_request.stop_on_error:
                            break
            
            # Count successes and failures for parallel execution
            if batch_request.parallel_execution:
                for result in results:
                    if isinstance(result, Exception):
                        failed_count += 1
                    elif hasattr(result, 'status') and result.status == ResponseStatus.SUCCESS:
                        successful_count += 1
                    else:
                        failed_count += 1
            
            total_execution_time = (datetime.now() - start_time).total_seconds()
            
            return BatchResponse(
                message=f"Batch execution completed: {successful_count} successful, {failed_count} failed",
                results=[r for r in results if not isinstance(r, Exception)],
                successful_count=successful_count,
                failed_count=failed_count,
                total_execution_time=total_execution_time
            )
            
        except Exception as e:
            logger.error(f"Failed to execute batch requests: {e}")
            return BatchResponse(
                status=ResponseStatus.ERROR,
                message=f"Batch execution failed: {str(e)}",
                results=[],
                successful_count=0,
                failed_count=len(batch_request.requests)
            )
    
    async def _get_analysis_data(self, data_source: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Get data for pandas analysis."""
        try:
            # This would typically query the database or use cached data
            # For now, return None to indicate no data available
            # In a real implementation, this would:
            # 1. Query the database based on data_source
            # 2. Return cached data from previous SQL queries
            # 3. Load data from files or external sources
            
            if data_source:
                # Try to get data from database
                sql_agent = self._agent_instances["sql_agent"]
                result = sql_agent.ejecutar_sql_directo(f"SELECT * FROM {data_source} LIMIT 1000")
                
                if result.get("success") and result.get("data"):
                    return pd.DataFrame(result["data"])
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get analysis data: {e}")
            return None
