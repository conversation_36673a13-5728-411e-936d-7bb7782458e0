"""
Minimal installation script for the Database Analyst System.
Installs only the essential dependencies needed to run the core functionality.
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a single package."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} installed successfully")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ Failed to install {package}")
        return False

def main():
    """Install minimal dependencies."""
    print("🚀 Installing minimal dependencies for Database Analyst System...")
    print("=" * 60)
    
    # Essential packages only
    essential_packages = [
        "streamlit>=1.28.0",
        "pandas>=2.0.0",
        "plotly>=5.0.0",
        "numpy>=1.24.0",
        "sqlalchemy>=2.0.0",
        "python-dotenv>=1.0.0"
    ]
    
    success_count = 0
    total_packages = len(essential_packages)
    
    for package in essential_packages:
        print(f"\n📦 Installing {package}...")
        if install_package(package):
            success_count += 1
    
    print("\n" + "=" * 60)
    print(f"Installation Summary: {success_count}/{total_packages} packages installed")
    
    if success_count == total_packages:
        print("🎉 All essential packages installed successfully!")
        print("\n📋 Next Steps:")
        print("1. Run: streamlit run simple_app.py")
        print("2. Or run: python simple_app.py")
        print("\n💡 This will start the simplified version of the Database Analyst System")
    else:
        print("⚠️  Some packages failed to install. You can still try running the application.")
        print("Run: streamlit run simple_app.py")
    
    # Create basic .env file if it doesn't exist
    if not os.path.exists('.env'):
        with open('.env', 'w') as f:
            f.write("""# Database Analyst System Configuration
# Basic SQLite database (no additional setup required)
DATABASE_URL=sqlite:///financial_data.db

# Optional: Add your Groq API key for advanced features
# GROQ_API_KEY=your_api_key_here

# Application settings
DEBUG=False
LOG_LEVEL=INFO
""")
        print("✅ Created basic .env configuration file")

if __name__ == "__main__":
    main()
