"""
Advanced chart factory for creating sophisticated financial and analytical visualizations.
Provides intelligent chart selection and customization based on data characteristics.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple, Union
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import json
from dataclasses import dataclass
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataPattern(Enum):
    """Enumeration of data patterns for intelligent chart selection."""
    TIME_SERIES = "time_series"
    CATEGORICAL = "categorical"
    NUMERICAL_DISTRIBUTION = "numerical_distribution"
    CORRELATION = "correlation"
    COMPARISON = "comparison"
    COMPOSITION = "composition"
    RELATIONSHIP = "relationship"
    TREND = "trend"

@dataclass
class ChartRecommendation:
    """Data class for chart recommendations."""
    chart_type: str
    confidence: float
    reason: str
    data_pattern: DataPattern
    suggested_config: Dict[str, Any]

class FabricaGraficos:
    """Advanced chart factory with intelligent visualization selection."""
    
    def __init__(self):
        """Initialize chart factory."""
        self.color_palettes = {
            "financial": ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd"],
            "professional": ["#2E86AB", "#A23B72", "#F18F01", "#C73E1D", "#592E83"],
            "modern": ["#264653", "#2a9d8f", "#e9c46a", "#f4a261", "#e76f51"],
            "cool": ["#003f5c", "#2f4b7c", "#665191", "#a05195", "#d45087"]
        }
        
        self.chart_patterns = {
            DataPattern.TIME_SERIES: ["line", "area", "candlestick"],
            DataPattern.CATEGORICAL: ["bar", "pie", "donut"],
            DataPattern.NUMERICAL_DISTRIBUTION: ["histogram", "box", "violin"],
            DataPattern.CORRELATION: ["heatmap", "scatter_matrix"],
            DataPattern.COMPARISON: ["bar", "radar", "parallel_coordinates"],
            DataPattern.COMPOSITION: ["pie", "treemap", "sunburst"],
            DataPattern.RELATIONSHIP: ["scatter", "bubble", "regression"],
            DataPattern.TREND: ["line", "area", "slope"]
        }
    
    def analizar_datos_y_recomendar(self, df: pd.DataFrame, objetivo: str = "general") -> List[ChartRecommendation]:
        """Analyze data and recommend appropriate visualizations."""
        try:
            logger.info(f"Analyzing data for chart recommendations: {df.shape}")
            
            recommendations = []
            
            # Analyze data characteristics
            data_info = self._analyze_data_characteristics(df)
            
            # Generate recommendations based on data patterns
            for pattern, confidence in data_info["patterns"].items():
                if confidence > 0.3:  # Minimum confidence threshold
                    chart_types = self.chart_patterns.get(pattern, [])
                    
                    for chart_type in chart_types[:2]:  # Top 2 chart types per pattern
                        recommendation = ChartRecommendation(
                            chart_type=chart_type,
                            confidence=confidence,
                            reason=self._generate_recommendation_reason(pattern, chart_type, data_info),
                            data_pattern=pattern,
                            suggested_config=self._generate_chart_config(chart_type, df, data_info)
                        )
                        recommendations.append(recommendation)
            
            # Sort by confidence
            recommendations.sort(key=lambda x: x.confidence, reverse=True)
            
            return recommendations[:5]  # Return top 5 recommendations
            
        except Exception as e:
            logger.error(f"Error analyzing data for recommendations: {e}")
            return []
    
    def crear_grafico_inteligente(self, df: pd.DataFrame, chart_type: str, 
                                config: Optional[Dict[str, Any]] = None) -> go.Figure:
        """Create intelligent chart with automatic configuration."""
        try:
            if config is None:
                data_info = self._analyze_data_characteristics(df)
                config = self._generate_chart_config(chart_type, df, data_info)
            
            # Chart creation methods
            chart_creators = {
                "line": self._create_advanced_line_chart,
                "bar": self._create_advanced_bar_chart,
                "scatter": self._create_advanced_scatter_chart,
                "heatmap": self._create_advanced_heatmap,
                "histogram": self._create_advanced_histogram,
                "box": self._create_advanced_box_plot,
                "pie": self._create_advanced_pie_chart,
                "area": self._create_advanced_area_chart,
                "candlestick": self._create_advanced_candlestick,
                "bubble": self._create_advanced_bubble_chart,
                "violin": self._create_advanced_violin_plot,
                "radar": self._create_advanced_radar_chart,
                "treemap": self._create_advanced_treemap,
                "sunburst": self._create_advanced_sunburst,
                "parallel_coordinates": self._create_parallel_coordinates,
                "scatter_matrix": self._create_scatter_matrix
            }
            
            creator = chart_creators.get(chart_type)
            if creator:
                return creator(df, config)
            else:
                logger.warning(f"Chart type {chart_type} not supported")
                return self._create_fallback_chart(df, config)
                
        except Exception as e:
            logger.error(f"Error creating intelligent chart: {e}")
            return self._create_fallback_chart(df, config or {})
    
    def _analyze_data_characteristics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze data characteristics to determine visualization patterns."""
        info = {
            "shape": df.shape,
            "numeric_columns": df.select_dtypes(include=[np.number]).columns.tolist(),
            "categorical_columns": df.select_dtypes(include=['object', 'category']).columns.tolist(),
            "datetime_columns": df.select_dtypes(include=['datetime64']).columns.tolist(),
            "patterns": {}
        }
        
        # Detect patterns
        patterns = {}
        
        # Time series pattern
        if info["datetime_columns"] and info["numeric_columns"]:
            patterns[DataPattern.TIME_SERIES] = 0.9
        
        # Categorical pattern
        if info["categorical_columns"]:
            patterns[DataPattern.CATEGORICAL] = 0.8
        
        # Numerical distribution pattern
        if len(info["numeric_columns"]) >= 1:
            patterns[DataPattern.NUMERICAL_DISTRIBUTION] = 0.7
        
        # Correlation pattern
        if len(info["numeric_columns"]) >= 2:
            patterns[DataPattern.CORRELATION] = 0.6
        
        # Relationship pattern
        if len(info["numeric_columns"]) >= 2:
            patterns[DataPattern.RELATIONSHIP] = 0.5
        
        info["patterns"] = patterns
        return info
    
    def _generate_recommendation_reason(self, pattern: DataPattern, chart_type: str, 
                                      data_info: Dict[str, Any]) -> str:
        """Generate explanation for chart recommendation."""
        reasons = {
            (DataPattern.TIME_SERIES, "line"): "Line chart is ideal for showing trends over time",
            (DataPattern.TIME_SERIES, "area"): "Area chart effectively shows volume changes over time",
            (DataPattern.CATEGORICAL, "bar"): "Bar chart clearly compares categorical values",
            (DataPattern.CATEGORICAL, "pie"): "Pie chart shows composition of categorical data",
            (DataPattern.NUMERICAL_DISTRIBUTION, "histogram"): "Histogram reveals data distribution patterns",
            (DataPattern.CORRELATION, "heatmap"): "Heatmap visualizes correlation between variables",
            (DataPattern.RELATIONSHIP, "scatter"): "Scatter plot reveals relationships between variables"
        }
        
        return reasons.get((pattern, chart_type), f"{chart_type} chart recommended for {pattern.value} data")
    
    def _generate_chart_config(self, chart_type: str, df: pd.DataFrame, 
                             data_info: Dict[str, Any]) -> Dict[str, Any]:
        """Generate intelligent chart configuration."""
        config = {
            "title": f"{chart_type.title()} Chart",
            "theme": "plotly_white",
            "color_palette": "financial",
            "show_legend": True,
            "interactive": True
        }
        
        # Auto-select columns based on chart type and data
        if chart_type in ["line", "area", "bar"] and data_info["datetime_columns"] and data_info["numeric_columns"]:
            config["x_column"] = data_info["datetime_columns"][0]
            config["y_column"] = data_info["numeric_columns"][0]
        elif chart_type == "scatter" and len(data_info["numeric_columns"]) >= 2:
            config["x_column"] = data_info["numeric_columns"][0]
            config["y_column"] = data_info["numeric_columns"][1]
        elif chart_type == "histogram" and data_info["numeric_columns"]:
            config["x_column"] = data_info["numeric_columns"][0]
        elif chart_type == "pie" and data_info["categorical_columns"] and data_info["numeric_columns"]:
            config["labels_column"] = data_info["categorical_columns"][0]
            config["values_column"] = data_info["numeric_columns"][0]
        
        return config
    
    def _create_advanced_line_chart(self, df: pd.DataFrame, config: Dict[str, Any]) -> go.Figure:
        """Create advanced line chart with multiple features."""
        fig = go.Figure()
        
        x_col = config.get("x_column")
        y_col = config.get("y_column")
        
        if x_col and y_col and x_col in df.columns and y_col in df.columns:
            # Main line
            fig.add_trace(go.Scatter(
                x=df[x_col],
                y=df[y_col],
                mode='lines+markers',
                name=y_col,
                line=dict(width=2, color=self.color_palettes["financial"][0]),
                marker=dict(size=4)
            ))
            
            # Add moving average if enough data points
            if len(df) > 20:
                ma_20 = df[y_col].rolling(window=20).mean()
                fig.add_trace(go.Scatter(
                    x=df[x_col],
                    y=ma_20,
                    mode='lines',
                    name='20-period MA',
                    line=dict(width=1, dash='dash', color=self.color_palettes["financial"][1])
                ))
            
            # Add trend line
            if len(df) > 10:
                z = np.polyfit(range(len(df)), df[y_col].fillna(df[y_col].mean()), 1)
                trend_line = np.poly1d(z)(range(len(df)))
                fig.add_trace(go.Scatter(
                    x=df[x_col],
                    y=trend_line,
                    mode='lines',
                    name='Trend',
                    line=dict(width=1, dash='dot', color=self.color_palettes["financial"][2])
                ))
        
        fig.update_layout(
            title=config.get("title", "Advanced Line Chart"),
            xaxis_title=x_col,
            yaxis_title=y_col,
            template=config.get("theme", "plotly_white"),
            hovermode='x unified',
            showlegend=config.get("show_legend", True)
        )
        
        return fig
    
    def _create_advanced_candlestick(self, df: pd.DataFrame, config: Dict[str, Any]) -> go.Figure:
        """Create advanced candlestick chart for financial data."""
        # Try to find OHLC columns
        ohlc_mapping = {}
        for col in df.columns:
            col_lower = col.lower()
            if 'open' in col_lower:
                ohlc_mapping['open'] = col
            elif 'high' in col_lower:
                ohlc_mapping['high'] = col
            elif 'low' in col_lower:
                ohlc_mapping['low'] = col
            elif 'close' in col_lower:
                ohlc_mapping['close'] = col
        
        if len(ohlc_mapping) >= 4:
            fig = make_subplots(
                rows=2, cols=1,
                shared_xaxes=True,
                vertical_spacing=0.1,
                subplot_titles=('Price', 'Volume'),
                row_width=[0.7, 0.3]
            )
            
            # Candlestick chart
            x_col = config.get("x_column") or df.index
            fig.add_trace(
                go.Candlestick(
                    x=x_col,
                    open=df[ohlc_mapping['open']],
                    high=df[ohlc_mapping['high']],
                    low=df[ohlc_mapping['low']],
                    close=df[ohlc_mapping['close']],
                    name="Price"
                ),
                row=1, col=1
            )
            
            # Volume chart if available
            volume_cols = [col for col in df.columns if 'volume' in col.lower()]
            if volume_cols:
                fig.add_trace(
                    go.Bar(
                        x=x_col,
                        y=df[volume_cols[0]],
                        name="Volume",
                        marker_color='rgba(158,202,225,0.8)'
                    ),
                    row=2, col=1
                )
            
            fig.update_layout(
                title=config.get("title", "Advanced Candlestick Chart"),
                template=config.get("theme", "plotly_white"),
                xaxis_rangeslider_visible=False
            )
            
            return fig
        else:
            # Fallback to line chart
            return self._create_advanced_line_chart(df, config)
    
    def _create_advanced_heatmap(self, df: pd.DataFrame, config: Dict[str, Any]) -> go.Figure:
        """Create advanced correlation heatmap."""
        numeric_df = df.select_dtypes(include=[np.number])
        
        if numeric_df.shape[1] > 1:
            corr_matrix = numeric_df.corr()
            
            # Create mask for upper triangle
            mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
            
            fig = go.Figure(data=go.Heatmap(
                z=corr_matrix.values,
                x=corr_matrix.columns,
                y=corr_matrix.columns,
                colorscale='RdBu',
                zmid=0,
                text=np.round(corr_matrix.values, 2),
                texttemplate="%{text}",
                textfont={"size": 10},
                hoverongaps=False
            ))
            
            fig.update_layout(
                title=config.get("title", "Correlation Heatmap"),
                template=config.get("theme", "plotly_white"),
                width=600,
                height=600
            )
            
            return fig
        else:
            return self._create_fallback_chart(df, config)
    
    def _create_advanced_bubble_chart(self, df: pd.DataFrame, config: Dict[str, Any]) -> go.Figure:
        """Create advanced bubble chart."""
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        
        if len(numeric_cols) >= 3:
            fig = go.Figure()
            
            fig.add_trace(go.Scatter(
                x=df[numeric_cols[0]],
                y=df[numeric_cols[1]],
                mode='markers',
                marker=dict(
                    size=df[numeric_cols[2]],
                    sizemode='diameter',
                    sizeref=2.*max(df[numeric_cols[2]])/(40.**2),
                    sizemin=4,
                    color=df[numeric_cols[0]],
                    colorscale='Viridis',
                    showscale=True
                ),
                text=df.index,
                hovertemplate=f'<b>%{{text}}</b><br>{numeric_cols[0]}: %{{x}}<br>{numeric_cols[1]}: %{{y}}<br>{numeric_cols[2]}: %{{marker.size}}<extra></extra>'
            ))
            
            fig.update_layout(
                title=config.get("title", "Advanced Bubble Chart"),
                xaxis_title=numeric_cols[0],
                yaxis_title=numeric_cols[1],
                template=config.get("theme", "plotly_white")
            )
            
            return fig
        else:
            return self._create_advanced_scatter_chart(df, config)
    
    def _create_fallback_chart(self, df: pd.DataFrame, config: Dict[str, Any]) -> go.Figure:
        """Create fallback chart when specific chart type fails."""
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        
        if numeric_cols:
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                y=df[numeric_cols[0]],
                mode='lines+markers',
                name=numeric_cols[0]
            ))
            
            fig.update_layout(
                title=config.get("title", "Data Visualization"),
                template=config.get("theme", "plotly_white")
            )
            
            return fig
        else:
            # Empty chart
            fig = go.Figure()
            fig.add_annotation(
                text="No suitable data for visualization",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False,
                font=dict(size=16)
            )
            
            fig.update_layout(
                title="No Data Available",
                template="plotly_white"
            )
            
            return fig
    
    # Placeholder methods for other chart types
    def _create_advanced_bar_chart(self, df: pd.DataFrame, config: Dict[str, Any]) -> go.Figure:
        """Create advanced bar chart."""
        return self._create_fallback_chart(df, config)
    
    def _create_advanced_scatter_chart(self, df: pd.DataFrame, config: Dict[str, Any]) -> go.Figure:
        """Create advanced scatter chart."""
        return self._create_fallback_chart(df, config)
    
    def _create_advanced_histogram(self, df: pd.DataFrame, config: Dict[str, Any]) -> go.Figure:
        """Create advanced histogram."""
        return self._create_fallback_chart(df, config)
    
    def _create_advanced_box_plot(self, df: pd.DataFrame, config: Dict[str, Any]) -> go.Figure:
        """Create advanced box plot."""
        return self._create_fallback_chart(df, config)
    
    def _create_advanced_pie_chart(self, df: pd.DataFrame, config: Dict[str, Any]) -> go.Figure:
        """Create advanced pie chart."""
        return self._create_fallback_chart(df, config)
    
    def _create_advanced_area_chart(self, df: pd.DataFrame, config: Dict[str, Any]) -> go.Figure:
        """Create advanced area chart."""
        return self._create_fallback_chart(df, config)
    
    def _create_advanced_violin_plot(self, df: pd.DataFrame, config: Dict[str, Any]) -> go.Figure:
        """Create advanced violin plot."""
        return self._create_fallback_chart(df, config)
    
    def _create_advanced_radar_chart(self, df: pd.DataFrame, config: Dict[str, Any]) -> go.Figure:
        """Create advanced radar chart."""
        return self._create_fallback_chart(df, config)
    
    def _create_advanced_treemap(self, df: pd.DataFrame, config: Dict[str, Any]) -> go.Figure:
        """Create advanced treemap."""
        return self._create_fallback_chart(df, config)
    
    def _create_advanced_sunburst(self, df: pd.DataFrame, config: Dict[str, Any]) -> go.Figure:
        """Create advanced sunburst chart."""
        return self._create_fallback_chart(df, config)
    
    def _create_parallel_coordinates(self, df: pd.DataFrame, config: Dict[str, Any]) -> go.Figure:
        """Create parallel coordinates plot."""
        return self._create_fallback_chart(df, config)
    
    def _create_scatter_matrix(self, df: pd.DataFrame, config: Dict[str, Any]) -> go.Figure:
        """Create scatter matrix."""
        return self._create_fallback_chart(df, config)
