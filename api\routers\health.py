"""
Health check endpoints for monitoring system status.
"""

import logging
import time
from typing import Dict, Any
from datetime import datetime
from fastapi import APIRouter, Depends, status
from fastapi.responses import JSONResponse

from api.models.common_models import (
    HealthResponse, HealthStatus, ComponentHealth, 
    SystemStatusResponse, SystemMetrics
)
from api.dependencies import (
    get_database_config_dependency, get_agent_coordinator_dependency,
    verify_database_health, verify_agent_health
)
from database.database_config import DatabaseConfig
from agents.agent_coordinator import CoordinadorAgentes

logger = logging.getLogger(__name__)

router = APIRouter()

# Track application start time for uptime calculation
_start_time = time.time()


@router.get("/", response_model=HealthResponse, status_code=status.HTTP_200_OK)
async def health_check(
    db_config: DatabaseConfig = Depends(get_database_config_dependency),
    coordinator: CoordinadorAgentes = Depends(get_agent_coordinator_dependency)
):
    """
    Comprehensive health check endpoint.
    
    Returns the overall health status of the system including:
    - Database connectivity
    - Agent availability
    - System uptime
    - Component status
    """
    try:
        components = []
        overall_healthy = True
        
        # Check database health
        try:
            db_healthy = db_config.test_connection()
            db_response_time = await _measure_database_response_time(db_config)
            
            components.append(ComponentHealth(
                name="database",
                status=HealthStatus.HEALTHY if db_healthy else HealthStatus.UNHEALTHY,
                message="Database connection active" if db_healthy else "Database connection failed",
                response_time=db_response_time
            ))
            
            if not db_healthy:
                overall_healthy = False
                
        except Exception as e:
            components.append(ComponentHealth(
                name="database",
                status=HealthStatus.UNHEALTHY,
                message=f"Database error: {str(e)}"
            ))
            overall_healthy = False
        
        # Check agent coordinator health
        try:
            agent_count = len(coordinator.agent_registry)
            agent_healthy = agent_count > 0
            
            components.append(ComponentHealth(
                name="agent_coordinator",
                status=HealthStatus.HEALTHY if agent_healthy else HealthStatus.UNHEALTHY,
                message=f"{agent_count} agents registered" if agent_healthy else "No agents available"
            ))
            
            if not agent_healthy:
                overall_healthy = False
                
        except Exception as e:
            components.append(ComponentHealth(
                name="agent_coordinator",
                status=HealthStatus.UNHEALTHY,
                message=f"Agent coordinator error: {str(e)}"
            ))
            overall_healthy = False
        
        # Check individual agents
        for agent_type, agent in coordinator.agent_registry.items():
            try:
                # Basic agent health check
                agent_status = HealthStatus.HEALTHY
                agent_message = f"{agent_type.value} agent available"
                
                components.append(ComponentHealth(
                    name=agent_type.value,
                    status=agent_status,
                    message=agent_message
                ))
                
            except Exception as e:
                components.append(ComponentHealth(
                    name=agent_type.value,
                    status=HealthStatus.UNHEALTHY,
                    message=f"Agent error: {str(e)}"
                ))
                overall_healthy = False
        
        # Calculate uptime
        uptime = time.time() - _start_time
        
        # Determine overall health
        overall_status = HealthStatus.HEALTHY if overall_healthy else HealthStatus.UNHEALTHY
        
        return HealthResponse(
            message="Health check completed",
            overall_health=overall_status,
            components=components,
            uptime=uptime,
            version="1.0.0"
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthResponse(
            message=f"Health check failed: {str(e)}",
            overall_health=HealthStatus.UNHEALTHY,
            components=[],
            uptime=time.time() - _start_time,
            version="1.0.0"
        )


@router.get("/live", status_code=status.HTTP_200_OK)
async def liveness_probe():
    """
    Simple liveness probe for container orchestration.
    Returns 200 if the application is running.
    """
    return {"status": "alive", "timestamp": datetime.now().isoformat()}


@router.get("/ready", status_code=status.HTTP_200_OK)
async def readiness_probe(
    db_healthy: bool = Depends(verify_database_health),
    agents_healthy: bool = Depends(verify_agent_health)
):
    """
    Readiness probe for container orchestration.
    Returns 200 if the application is ready to serve requests.
    """
    if db_healthy and agents_healthy:
        return {
            "status": "ready",
            "database": "healthy",
            "agents": "healthy",
            "timestamp": datetime.now().isoformat()
        }
    else:
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "not_ready",
                "database": "healthy" if db_healthy else "unhealthy",
                "agents": "healthy" if agents_healthy else "unhealthy",
                "timestamp": datetime.now().isoformat()
            }
        )


@router.get("/status", response_model=SystemStatusResponse, status_code=status.HTTP_200_OK)
async def system_status(
    coordinator: CoordinadorAgentes = Depends(get_agent_coordinator_dependency),
    db_config: DatabaseConfig = Depends(get_database_config_dependency)
):
    """
    Detailed system status with metrics and performance data.
    """
    try:
        # Calculate system metrics
        active_workflows = len([w for w in coordinator.workflows.values() 
                              if w.status.value in ["pending", "in_progress"]])
        
        # Get database status
        db_status = HealthStatus.HEALTHY if db_config.test_connection() else HealthStatus.UNHEALTHY
        
        # Get agent status
        agent_status = {}
        for agent_type in coordinator.agent_registry.keys():
            try:
                agent_status[agent_type.value] = HealthStatus.HEALTHY
            except:
                agent_status[agent_type.value] = HealthStatus.UNHEALTHY
        
        # Create metrics
        metrics = SystemMetrics(
            active_workflows=active_workflows,
            total_requests=0,  # Would be tracked by middleware
            average_response_time=0.0,  # Would be calculated from logs
            error_rate=0.0  # Would be calculated from logs
        )
        
        return SystemStatusResponse(
            message="System status retrieved successfully",
            metrics=metrics,
            database_status=db_status,
            agent_status=agent_status
        )
        
    except Exception as e:
        logger.error(f"Failed to get system status: {e}")
        return SystemStatusResponse(
            message=f"Failed to get system status: {str(e)}",
            metrics=SystemMetrics(),
            database_status=HealthStatus.UNHEALTHY,
            agent_status={}
        )


async def _measure_database_response_time(db_config: DatabaseConfig) -> float:
    """Measure database response time."""
    try:
        start_time = time.time()
        with db_config.get_connection() as conn:
            conn.execute("SELECT 1")
        return time.time() - start_time
    except:
        return -1.0
