"""
FastAPI dependency injection for database connections and services.
Provides reusable dependencies for API endpoints.
"""

import logging
from typing import Generator, Optional
from fastapi import Depends, HTTPException, status
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Session

from database.database_config import get_database_config, DatabaseConfig
from agents.agent_coordinator import get_agent_coordinator, CoordinadorAgentes
from api.services.workflow_service import WorkflowService
from api.services.agent_service import AgentService
from api.services.upload_service import UploadService
from config.settings import get_settings, Settings

logger = logging.getLogger(__name__)


# Global service instances
_workflow_service: Optional[WorkflowService] = None
_agent_service: Optional[AgentService] = None
_upload_service: Optional[UploadService] = None


def get_database_config_dependency() -> DatabaseConfig:
    """Dependency to get database configuration."""
    try:
        return get_database_config()
    except Exception as e:
        logger.error(f"Failed to get database config: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database configuration unavailable"
        )


def get_database_engine(
    db_config: DatabaseConfig = Depends(get_database_config_dependency)
) -> Engine:
    """Dependency to get database engine."""
    try:
        engine = db_config.initialize_engine()
        if engine is None:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Database engine not available"
            )
        return engine
    except Exception as e:
        logger.error(f"Failed to get database engine: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database connection failed"
        )


def get_database_connection(
    db_config: DatabaseConfig = Depends(get_database_config_dependency)
) -> Generator:
    """Dependency to get database connection with automatic cleanup."""
    try:
        with db_config.get_connection() as connection:
            yield connection
    except Exception as e:
        logger.error(f"Database connection error: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database connection failed"
        )


def get_agent_coordinator_dependency() -> CoordinadorAgentes:
    """Dependency to get agent coordinator."""
    try:
        coordinator = get_agent_coordinator()
        if coordinator is None:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Agent coordinator not available"
            )
        return coordinator
    except Exception as e:
        logger.error(f"Failed to get agent coordinator: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Agent coordinator unavailable"
        )


def get_workflow_service() -> WorkflowService:
    """Dependency to get workflow service (singleton)."""
    global _workflow_service
    
    try:
        if _workflow_service is None:
            _workflow_service = WorkflowService()
            logger.info("Workflow service initialized")
        
        return _workflow_service
    except Exception as e:
        logger.error(f"Failed to get workflow service: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Workflow service unavailable"
        )


def get_agent_service() -> AgentService:
    """Dependency to get agent service (singleton)."""
    global _agent_service
    
    try:
        if _agent_service is None:
            _agent_service = AgentService()
            logger.info("Agent service initialized")
        
        return _agent_service
    except Exception as e:
        logger.error(f"Failed to get agent service: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Agent service unavailable"
        )


def get_upload_service() -> UploadService:
    """Dependency to get upload service (singleton)."""
    global _upload_service

    try:
        if _upload_service is None:
            _upload_service = UploadService()
            logger.info("Upload service initialized")

        return _upload_service
    except Exception as e:
        logger.error(f"Failed to get upload service: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Upload service unavailable"
        )


def get_settings_dependency() -> Settings:
    """Dependency to get application settings."""
    try:
        return get_settings()
    except Exception as e:
        logger.error(f"Failed to get settings: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Application settings unavailable"
        )


def verify_database_health(
    db_config: DatabaseConfig = Depends(get_database_config_dependency)
) -> bool:
    """Dependency to verify database health."""
    try:
        return db_config.test_connection()
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return False


def verify_agent_health(
    coordinator: CoordinadorAgentes = Depends(get_agent_coordinator_dependency)
) -> bool:
    """Dependency to verify agent coordinator health."""
    try:
        # Basic health check - verify coordinator has agents registered
        return len(coordinator.agent_registry) > 0
    except Exception as e:
        logger.error(f"Agent health check failed: {e}")
        return False


class DatabaseHealthCheck:
    """Database health check dependency class."""
    
    def __init__(self, required: bool = True):
        self.required = required
    
    def __call__(self, db_healthy: bool = Depends(verify_database_health)) -> bool:
        if self.required and not db_healthy:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Database is not healthy"
            )
        return db_healthy


class AgentHealthCheck:
    """Agent health check dependency class."""
    
    def __init__(self, required: bool = True):
        self.required = required
    
    def __call__(self, agents_healthy: bool = Depends(verify_agent_health)) -> bool:
        if self.required and not agents_healthy:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Agents are not healthy"
            )
        return agents_healthy


# Pre-configured dependency instances
require_database = DatabaseHealthCheck(required=True)
check_database = DatabaseHealthCheck(required=False)
require_agents = AgentHealthCheck(required=True)
check_agents = AgentHealthCheck(required=False)


def get_pagination_params(page: int = 1, page_size: int = 20) -> dict:
    """Dependency for pagination parameters with validation."""
    if page < 1:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Page number must be >= 1"
        )
    
    if page_size < 1 or page_size > 100:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Page size must be between 1 and 100"
        )
    
    return {
        "page": page,
        "page_size": page_size,
        "offset": (page - 1) * page_size
    }


def validate_workflow_id(workflow_id: str) -> str:
    """Dependency to validate workflow ID format."""
    if not workflow_id or len(workflow_id) < 5:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid workflow ID format"
        )
    return workflow_id


def validate_agent_type(agent_type: str) -> str:
    """Dependency to validate agent type."""
    valid_types = ["sql_agent", "pandas_agent", "schema_analyzer"]
    
    if agent_type not in valid_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid agent type. Must be one of: {', '.join(valid_types)}"
        )
    
    return agent_type


# Cleanup functions for application shutdown
def cleanup_services():
    """Cleanup function to be called on application shutdown."""
    global _workflow_service, _agent_service
    
    try:
        # Close database connections
        db_config = get_database_config()
        db_config.close_engine()
        logger.info("Database connections closed")
        
        # Reset service instances
        _workflow_service = None
        _agent_service = None
        logger.info("Services cleaned up")
        
    except Exception as e:
        logger.error(f"Error during cleanup: {e}")


# Export commonly used dependencies
__all__ = [
    "get_database_config_dependency",
    "get_database_engine", 
    "get_database_connection",
    "get_agent_coordinator_dependency",
    "get_workflow_service",
    "get_agent_service",
    "get_settings_dependency",
    "require_database",
    "check_database",
    "require_agents", 
    "check_agents",
    "get_pagination_params",
    "validate_workflow_id",
    "validate_agent_type",
    "cleanup_services"
]
