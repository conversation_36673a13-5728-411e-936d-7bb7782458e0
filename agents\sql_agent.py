"""
SQL Agent for natural language to SQL query translation and execution.
Integrates with LangChain SQL toolkit for secure database interactions.
"""

import logging
from typing import Dict, Any, List, Optional
from langchain_community.agent_toolkits import create_sql_agent
from langchain_community.utilities import SQLDatabase
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_groq import ChatGroq
from langchain_core.tools import tool
from database.database_config import get_database_config
import pandas as pd
from datetime import datetime
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgenteSQLConsultas:
    """SQL Agent for natural language database queries."""
    
    def __init__(self):
        """Initialize SQL agent with database connection and LLM."""
        self.db_config = get_database_config()
        self.llm = ChatGroq(temperature=0, model="llama3-70b-8192")
        self.sql_db = None
        self.sql_agent = None
        self._initialize_sql_components()
        
        # Query validation prompt
        self.validation_prompt = PromptTemplate(
            input_variables=["query", "schema_info"],
            template="""
            You are a SQL security expert. Analyze the following SQL query for potential security risks:
            
            Query: {query}
            Database Schema: {schema_info}
            
            Check for:
            1. SQL injection attempts
            2. Unauthorized data access
            3. Destructive operations (DROP, DELETE, UPDATE without WHERE)
            4. Performance issues (missing WHERE clauses on large tables)
            
            Respond with either "SAFE" or "UNSAFE: [reason]"
            """
        )
        
        self.validation_chain = self.validation_prompt | self.llm | StrOutputParser()
    
    def _initialize_sql_components(self):
        """Initialize SQL database connection and agent."""
        try:
            # Initialize database engine
            engine = self.db_config.initialize_engine()
            
            # Create LangChain SQL database wrapper
            self.sql_db = SQLDatabase(engine=engine)
            
            # Create SQL agent with enhanced prompting
            self.sql_agent = create_sql_agent(
                llm=self.llm,
                db=self.sql_db,
                verbose=True,
                agent_type="openai-tools",
                handle_parsing_errors=True,
                max_iterations=5,
                early_stopping_method="generate"
            )
            
            logger.info("SQL agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize SQL agent: {e}")
            raise
    
    def _validate_query(self, query: str) -> bool:
        """Validate SQL query for security and safety."""
        try:
            # Get schema information
            schema_info = self._get_schema_summary()
            
            # Use LLM to validate query
            validation_result = self.validation_chain.invoke({
                "query": query,
                "schema_info": schema_info
            })
            
            if validation_result.strip().upper().startswith("SAFE"):
                return True
            else:
                logger.warning(f"Query validation failed: {validation_result}")
                return False
                
        except Exception as e:
            logger.error(f"Query validation error: {e}")
            return False
    
    def _get_schema_summary(self) -> str:
        """Get a summary of database schema for context."""
        try:
            tables = self.db_config.get_table_names()
            schema_summary = []
            
            for table in tables[:10]:  # Limit to first 10 tables
                schema = self.db_config.get_table_schema(table)
                if schema and 'columns' in schema:
                    columns = [col['name'] for col in schema['columns'][:5]]  # First 5 columns
                    schema_summary.append(f"{table}: {', '.join(columns)}")
            
            return "; ".join(schema_summary)
            
        except Exception as e:
            logger.error(f"Failed to get schema summary: {e}")
            return "Schema information unavailable"
    
    def _sanitize_query(self, query: str) -> str:
        """Basic query sanitization."""
        # Remove potentially dangerous keywords
        dangerous_patterns = [
            r'\bDROP\b', r'\bDELETE\b', r'\bUPDATE\b', r'\bINSERT\b',
            r'\bALTER\b', r'\bCREATE\b', r'\bTRUNCATE\b', r'\bEXEC\b'
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                logger.warning(f"Potentially dangerous SQL pattern detected: {pattern}")
                return ""
        
        return query
    
    def ejecutar_consulta_natural(self, consulta_natural: str) -> Dict[str, Any]:
        """Execute natural language query and return structured results."""
        try:
            logger.info(f"Processing natural language query: {consulta_natural}")
            
            # Enhanced prompt for better SQL generation
            enhanced_prompt = f"""
            You are a financial data analyst. Convert this natural language query to SQL:
            "{consulta_natural}"
            
            Guidelines:
            1. Only use SELECT statements
            2. Include appropriate WHERE clauses for performance
            3. Use proper JOIN syntax when needed
            4. Format dates appropriately
            5. Limit results to reasonable amounts (use LIMIT if needed)
            6. Focus on financial and analytical queries
            
            Current date: {datetime.now().strftime('%Y-%m-%d')}
            
            Generate the SQL query and execute it.
            """
            
            # Execute query through SQL agent
            result = self.sql_agent.invoke({"input": enhanced_prompt})
            
            # Extract the actual data if available
            output = result.get("output", "")
            
            return {
                "success": True,
                "query_result": output,
                "consulta_original": consulta_natural,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error executing natural language query: {e}")
            return {
                "success": False,
                "error": str(e),
                "consulta_original": consulta_natural,
                "timestamp": datetime.now().isoformat()
            }
    
    def ejecutar_sql_directo(self, sql_query: str) -> Dict[str, Any]:
        """Execute SQL query directly with validation."""
        try:
            logger.info(f"Executing direct SQL query: {sql_query[:100]}...")
            
            # Sanitize query
            sanitized_query = self._sanitize_query(sql_query)
            if not sanitized_query:
                return {
                    "success": False,
                    "error": "Query contains potentially dangerous operations",
                    "timestamp": datetime.now().isoformat()
                }
            
            # Validate query
            if not self._validate_query(sanitized_query):
                return {
                    "success": False,
                    "error": "Query failed security validation",
                    "timestamp": datetime.now().isoformat()
                }
            
            # Execute query
            results = self.db_config.execute_query(sanitized_query)
            
            return {
                "success": True,
                "data": results,
                "row_count": len(results),
                "query": sanitized_query,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error executing SQL query: {e}")
            return {
                "success": False,
                "error": str(e),
                "query": sql_query,
                "timestamp": datetime.now().isoformat()
            }
    
    def obtener_informacion_esquema(self) -> Dict[str, Any]:
        """Get database schema information."""
        try:
            tables = self.db_config.get_table_names()
            schema_info = {}
            
            for table in tables:
                schema_info[table] = self.db_config.get_table_schema(table)
            
            return {
                "success": True,
                "tables": tables,
                "schema_details": schema_info,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting schema information: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

@tool
def ConsultarBaseDatos(consulta_natural: str) -> Dict[str, Any]:
    """
    Execute natural language database queries.
    
    Args:
        consulta_natural (str): Natural language query about the database
        
    Returns:
        Dict[str, Any]: Query results and metadata
    """
    agente_sql = AgenteSQLConsultas()
    return agente_sql.ejecutar_consulta_natural(consulta_natural)

@tool
def EjecutarSQL(sql_query: str) -> Dict[str, Any]:
    """
    Execute SQL query directly with security validation.
    
    Args:
        sql_query (str): SQL query to execute
        
    Returns:
        Dict[str, Any]: Query results and metadata
    """
    agente_sql = AgenteSQLConsultas()
    return agente_sql.ejecutar_sql_directo(sql_query)
